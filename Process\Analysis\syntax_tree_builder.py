#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法树构建器 - 构建着色器代码的语法树结构并进行类型分析
"""

import ast
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from .code_line_analyzer import Code<PERSON><PERSON><PERSON><PERSON>y<PERSON>
from .type_inference_engine import DataType, type_inference_engine

class NodeType(Enum):
    """节点类型枚举"""
    VARIABLE = "variable"           # 变量
    LITERAL = "literal"            # 字面量
    OPERATOR = "operator"          # 运算符
    FUNCTION = "function"          # 函数调用
    ASSIGNMENT = "assignment"      # 赋值
    DECLARATION = "declaration"    # 声明
    MEMBER_ACCESS = "member_access" # 成员访问
    TYPE_CAST = "type_cast"        # 类型转换



@dataclass
class operationProcess:
    """运算过程数据结构"""
    string: str = ""  # 完整的运算表达式，比如"float a = b + c"
    left_dataType: List[DataType] = None  # 左操作数的数据类型
    right_dataType: List[DataType] = None # 右操作数的数据类型

    def __post_init__(self):
        if self.left_dataType is None:
            self.left_dataType = []
        if self.right_dataType is None:
            self.right_dataType = []

@dataclass
class ASTNode:
    """抽象语法树节点"""
    node_type: NodeType
    value: str                   # 节点值（变量名、运算符、函数名等）
    data_type: DataType          # 数据类型
    children: List['ASTNode']    # 子节点
    line_number: int             # 源代码行号
    position: int = 0            # 在行中的位置

    def add_child(self, child: 'ASTNode'):
        """添加子节点"""
        self.children.append(child)

    def __str__(self):
        return f"{self.node_type.value}({self.value}:{self.data_type.value})"

    def print_tree(self, indent: int = 0) -> str:
        """打印语法树结构"""
        prefix = "  " * indent
        result = f"{prefix}{self.node_type.value}({self.value}:{self.data_type.value})\n"

        for child in self.children:
            result += child.print_tree(indent + 1)

        return result


class LineAnalysisResult:
    """单行代码分析结果"""
    def __init__(self, line_number: int, line_content: str):
        self.line_number = line_number
        self.line_content = line_content
        self.syntax_tree: Optional[ASTNode] = None
        self.variable_types: Dict[str, DataType] = {}
        self.type_conversions: List[Dict] = []
        self.precision_issues: List[Dict] = []
        self.operation_process: List[operationProcess] = []
        self.message: str = ""



class SyntaxTreeBuilder:
    """语法树构建器 - 构建单行代码的语法树并进行分析"""

    def __init__(self):
        # 使用全局类型推断引擎
        self.type_engine = type_inference_engine

        # 初始化时清空日志文件
        self._init_log_files()

        # 数据类型映射
        self.type_mapping = {
            'float': DataType.FLOAT,
            'half': DataType.HALF,
            'int': DataType.INT,
            'uint': DataType.UINT,
            'bool': DataType.BOOL,
            'float2': DataType.FLOAT2,
            'float3': DataType.FLOAT3,
            'float4': DataType.FLOAT4,
            'half2': DataType.HALF2,
            'half3': DataType.HALF3,
            'half4': DataType.HALF4,
            'float3x3': DataType.FLOAT3X3,
            'float4x4': DataType.FLOAT4X4,
        }

        # 运算符优先级
        self.operator_precedence = {
            '=': 1,
            '||': 2,
            '&&': 3,
            '|': 4,
            '^': 5,
            '&': 6,
            '==': 7, '!=': 7,
            '<': 8, '<=': 8, '>': 8, '>=': 8,
            '+': 9, '-': 9,
            '*': 10, '/': 10, '%': 10,
            '!': 11, '~': 11, 'unary+': 11, 'unary-': 11,
            '.': 12, '[]': 12, '()': 12
        }

        # 临时变量计数器
        self.temp_var_counter = 0
        self.temp_var_types: Dict[str, DataType] = {}

    def _init_log_files(self):
        """初始化日志文件，清空之前的内容"""
        # 清空语法树日志文件
        with open("syntax_tree_log.txt", "w", encoding="utf-8") as f:
            f.write("=== 语法树分析日志 ===\n\n")

        # 清空运算过程日志文件
        with open("operation_process_log.txt", "w", encoding="utf-8") as f:
            f.write("=== 运算过程分析日志 ===\n\n")
        

    def analyze_line(self, line_content: str, line_number: int, global_var_types: Dict[str, DataType]) -> LineAnalysisResult:
        """分析单行代码，生成语法树和运算过程"""
        result = LineAnalysisResult(line_number, line_content)

        try:
            # 1. 构建语法树（只构建结构，不推断类型）
            result.syntax_tree = self._build_syntax_tree(line_content, line_number)

            # 打印语法树
            self.print_syntax_tree(result.syntax_tree, line_number, line_content)
            
            # 2. 更新变量类型信息
            self._extract_variable_types(result.syntax_tree, result.variable_types, global_var_types)

            # 打印语法树
            self.print_syntax_tree(result.syntax_tree, line_number, line_content)

            # 3. 生成运算过程
            result.operation_process = self._generate_operation_process(result.syntax_tree, global_var_types)

            # 打印运算过程
            self.print_operation_process(result.operation_process, line_number, line_content)

            # 4. 检查类型转换
            result.type_conversions = self._check_type_conversions(result.operation_process)

            # 5. 检查精度问题（暂时为空，后续添加）
            result.precision_issues = []

            result.message = "分析成功"

        except Exception as e:


            log_file_path = "syntax_tree_log.txt"
            with open(log_file_path, "a", encoding="utf-8") as f:
                f.write(f"分析失败: {str(e)} in {line_number} : {line_content}\n\n")
            result.message = f"分析失败: {str(e)}"

        return result


###########################构建语法树############################################
    def _build_syntax_tree(self, line_content: str, line_number: int) -> ASTNode:
        """构建单行代码的语法树 - 只构建结构，不推断类型"""
        # 清理代码行，移除注释和多余空格
        clean_line = self._clean_line(line_content)

        # 解析代码行，构建语法树（所有节点类型默认为 UNKNOWN）
        if '=' in clean_line and not any(op in clean_line for op in ['==', '!=', '<=', '>=']):
            # 赋值语句
            return self._parse_assignment(clean_line, line_number)
        elif self._is_control_flow_statement(clean_line):
            # 控制流语句（for、while、if、do-while等）
            return self._parse_control_flow(clean_line, line_number)
        else:
            # 表达式语句
            return self._parse_expression(clean_line, line_number)

    def _clean_line(self, line: str) -> str:
        """清理代码行"""
        # 移除注释
        line = re.sub(r'//.*$', '', line)
        # 移除分号
        line = line.rstrip(';')
        # 移除多余空格
        line = ' '.join(line.split())
        return line.strip()

    def _is_control_flow_statement(self, line: str) -> bool:
        """检查是否是控制流语句"""
        line = line.strip()
        # 检查各种控制流语句
        control_patterns = [
            r'^\s*for\s*\(',           # for循环
            r'^\s*while\s*\(',         # while循环
            r'^\s*if\s*\(',            # if语句
            r'^\s*else\s*{?',          # else语句
            r'^\s*}\s*while\s*\(',     # do-while结束
            r'^\s*do\s*{',             # do语句
            r'^\s*switch\s*\(',        # switch语句
            r'^\s*case\s+',            # case语句
            r'^\s*default\s*:',        # default语句
            r'^\s*break\s*;',          # break语句
            r'^\s*continue\s*;',       # continue语句
            r'^\s*return\s*',          # return语句
            r'^\s*{\s*$',              # 单独的开括号
            r'^\s*}\s*$',              # 单独的闭括号
        ]

        for pattern in control_patterns:
            if re.match(pattern, line):
                return True
        return False

    def _parse_control_flow(self, line: str, line_number: int) -> ASTNode:
        """解析控制流语句"""
        line = line.strip()

        # 根据不同的控制流类型创建相应的节点
        if line.startswith('for'):
            node_value = 'for'
        elif line.startswith('while') or '} while' in line:
            node_value = 'while'
        elif line.startswith('if'):
            node_value = 'if'
        elif line.startswith('else'):
            node_value = 'else'
        elif line.startswith('do'):
            node_value = 'do'
        elif line.startswith('switch'):
            node_value = 'switch'
        elif line.startswith('case'):
            node_value = 'case'
        elif line.startswith('default'):
            node_value = 'default'
        elif line.startswith('break'):
            node_value = 'break'
        elif line.startswith('continue'):
            node_value = 'continue'
        elif line.startswith('return'):
            node_value = 'return'
        elif line == '{':
            node_value = 'block_start'
        elif line == '}':
            node_value = 'block_end'
        else:
            node_value = 'control_flow'

        return ASTNode(
            node_type=NodeType.OPERATOR,  # 使用OPERATOR类型表示控制流
            value=node_value,
            data_type=DataType.UNKNOWN,
            children=[],
            line_number=line_number
        )

    def _parse_assignment(self, line: str, line_number: int) -> ASTNode:
        """解析赋值语句 - 只构建结构，不推断类型"""
        # 分割赋值语句
        parts = line.split('=', 1)
        if len(parts) != 2:
            raise ValueError(f"无效的赋值语句: {line}")

        left_part = parts[0].strip()
        right_part = parts[1].strip()

        # 创建赋值节点 - 类型默认为 UNKNOWN
        assignment_node = ASTNode(
            node_type=NodeType.ASSIGNMENT,
            value='=',
            data_type=DataType.UNKNOWN,
            children=[],
            line_number=line_number
        )

        # 解析左侧（变量声明或变量）- 类型默认为 UNKNOWN
        left_node = self._parse_left_side(left_part, line_number)
        assignment_node.add_child(left_node)

        # 解析右侧表达式 - 类型默认为 UNKNOWN
        if right_part.strip() == '{}':
            # 空的初始化列表
            right_node = ASTNode(
                node_type=NodeType.LITERAL,
                value='{}',
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )
        else:
            right_node = self._parse_expression(right_part, line_number)
        assignment_node.add_child(right_node)

        return assignment_node

    def _parse_left_side(self, left_part: str, line_number: int) -> ASTNode:
        """解析赋值语句的左侧 - 只构建结构，不推断类型"""
        # 检查是否是变量声明（支持 constant 关键字和结构体类型）
        type_pattern = r'(?:constant\s+)?(?:\b(float|half|int|uint|bool|float2|float3|float4|half2|half3|half4|float3x3|float4x4)\s+([a-zA-Z_][a-zA-Z0-9_]*)|([a-zA-Z_][a-zA-Z0-9_]*)\s+([a-zA-Z_][a-zA-Z0-9_]*))'
        match = re.match(type_pattern, left_part)

        if match:
            # 变量声明
            if match.group(1) and match.group(2):
                # 标准类型声明 (如 float a, constant half3 b)
                var_name = match.group(2)
            elif match.group(3) and match.group(4):
                # 结构体类型声明 (如 main0_out out)
                var_name = match.group(4)
            else:
                # 解析失败，当作普通变量处理
                return ASTNode(
                    node_type=NodeType.VARIABLE,
                    value=left_part,
                    data_type=DataType.UNKNOWN,
                    children=[],
                    line_number=line_number
                )

            return ASTNode(
                node_type=NodeType.DECLARATION,
                value=var_name,
                data_type=DataType.UNKNOWN,  # 类型默认为 UNKNOWN
                children=[],
                line_number=line_number
            )
        else:
            # 普通变量或成员访问
            if '.' in left_part:
                # 成员访问赋值 (如 _17899.z)
                return ASTNode(
                    node_type=NodeType.MEMBER_ACCESS,
                    value=left_part,
                    data_type=DataType.UNKNOWN,
                    children=[],
                    line_number=line_number
                )
            else:
                # 普通变量
                return ASTNode(
                    node_type=NodeType.VARIABLE,
                    value=left_part,
                    data_type=DataType.UNKNOWN,  # 类型默认为 UNKNOWN
                    children=[],
                    line_number=line_number
                )

    def _parse_expression(self, expr: str, line_number: int) -> ASTNode:
        """解析表达式 - 只构建结构，不推断类型"""
        # 创建词法分析器
        tokens = self._tokenize(expr)
        if not tokens:
            # 如果无法分词，返回一个未知类型的节点
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=expr,
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )

        # 创建解析器状态
        parser_state = {
            'tokens': tokens,
            'position': 0,
            'line_number': line_number
        }

        # 解析表达式
        return self._parse_additive_expression(parser_state)

    def _tokenize(self, expr: str) -> List[Dict]:
        """词法分析 - 将表达式分解为token"""
        tokens = []
        i = 0
        expr = expr.strip()

        while i < len(expr):
            # 跳过空白字符
            if expr[i].isspace():
                i += 1
                continue

            # 数字字面量
            if expr[i].isdigit() or (expr[i] == '.' and i + 1 < len(expr) and expr[i + 1].isdigit()):
                start = i
                while i < len(expr) and (expr[i].isdigit() or expr[i] == '.'):
                    i += 1
                if i < len(expr) and expr[i] == 'f':
                    i += 1
                tokens.append({'type': 'NUMBER', 'value': expr[start:i], 'pos': start})
                continue

            # 标识符和关键字（包括成员访问）
            if expr[i].isalpha() or expr[i] == '_':
                start = i
                # 解析完整的标识符，包括成员访问链和命名空间
                while i < len(expr):
                    if expr[i].isalnum() or expr[i] == '_':
                        i += 1
                    elif expr[i] == '.' and i + 1 < len(expr) and (expr[i + 1].isalpha() or expr[i + 1] == '_'):
                        i += 1
                    elif expr[i:i+2] == '::' and i + 2 < len(expr) and (expr[i + 2].isalpha() or expr[i + 2] == '_'):
                        i += 2  # 跳过 '::'
                    else:
                        break
                tokens.append({'type': 'IDENTIFIER', 'value': expr[start:i], 'pos': start})
                continue

            # 运算符 - 按长度排序，先匹配长的
            operators = ['==', '!=', '<=', '>=', '&&', '||', '++', '--', '+=', '-=', '*=', '/=',
                        '+', '-', '*', '/', '%', '=', '<', '>', '!', '&', '|', '^', '~']
            matched = False
            for op in operators:
                if expr[i:i+len(op)] == op:
                    tokens.append({'type': 'OPERATOR', 'value': op, 'pos': i})
                    i += len(op)
                    matched = True
                    break

            if matched:
                continue

            # 括号和分隔符
            if expr[i] in '()[]{},.;':
                tokens.append({'type': 'DELIMITER', 'value': expr[i], 'pos': i})
                i += 1
                continue

            # 未知字符，跳过
            i += 1

        return tokens

    def _peek_token(self, state: Dict) -> Dict:
        """查看当前token但不消费"""
        if state['position'] < len(state['tokens']):
            return state['tokens'][state['position']]
        return None

    def _consume_token(self, state: Dict) -> Dict:
        """消费当前token"""
        if state['position'] < len(state['tokens']):
            token = state['tokens'][state['position']]
            state['position'] += 1
            return token
        return None

    def _parse_additive_expression(self, state: Dict) -> ASTNode:
        """解析加法表达式 (+ -)"""
        left = self._parse_multiplicative_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['+', '-']:
                break

            op_token = self._consume_token(state)
            right = self._parse_multiplicative_expression(state)

            # 创建运算符节点
            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_multiplicative_expression(self, state: Dict) -> ASTNode:
        """解析乘法表达式 (* / %)"""
        left = self._parse_unary_expression(state)

        while True:
            token = self._peek_token(state)
            if not token or token['type'] != 'OPERATOR' or token['value'] not in ['*', '/', '%']:
                break

            op_token = self._consume_token(state)
            right = self._parse_unary_expression(state)

            # 创建运算符节点
            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[left, right],
                line_number=state['line_number']
            )
            left = op_node

        return left

    def _parse_unary_expression(self, state: Dict) -> ASTNode:
        """解析一元表达式 (- + ! ~)"""
        token = self._peek_token(state)

        if token and token['type'] == 'OPERATOR' and token['value'] in ['-', '+', '!', '~']:
            op_token = self._consume_token(state)
            operand = self._parse_unary_expression(state)

            # 创建一元运算符节点
            op_node = ASTNode(
                node_type=NodeType.OPERATOR,
                value=op_token['value'],
                data_type=DataType.UNKNOWN,
                children=[operand],
                line_number=state['line_number']
            )
            return op_node

        return self._parse_postfix_expression(state)

    def _parse_postfix_expression(self, state: Dict) -> ASTNode:
        """解析后缀表达式 (函数调用, 成员访问)"""
        left = self._parse_primary_expression(state)

        while True:
            token = self._peek_token(state)
            if not token:
                break

            if token['type'] == 'DELIMITER' and token['value'] == '(':
                # 函数调用
                self._consume_token(state)  # 消费 '('

                # 解析参数
                args = []
                while True:
                    token = self._peek_token(state)
                    if token and token['type'] == 'DELIMITER' and token['value'] == ')':
                        break

                    arg = self._parse_additive_expression(state)
                    if arg:
                        args.append(arg)

                    # 检查是否有逗号
                    token = self._peek_token(state)
                    if token and token['type'] == 'DELIMITER' and token['value'] == ',':
                        self._consume_token(state)  # 消费 ','
                    else:
                        break

                # 消费 ')'
                token = self._peek_token(state)
                if token and token['type'] == 'DELIMITER' and token['value'] == ')':
                    self._consume_token(state)

                # 创建函数调用节点
                func_node = ASTNode(
                    node_type=NodeType.FUNCTION,
                    value=left.value if left else 'unknown',
                    data_type=DataType.UNKNOWN,
                    children=args,
                    line_number=state['line_number']
                )
                left = func_node
            else:
                break

        return left

    def _parse_primary_expression(self, state: Dict) -> ASTNode:
        """解析基本表达式 (数字, 标识符, 括号表达式)"""
        token = self._peek_token(state)
        if not token:
            # 如果没有token，返回一个未知类型的节点
            return ASTNode(
                node_type=NodeType.LITERAL,
                value="",
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=state['line_number']
            )

        # 括号表达式
        if token['type'] == 'DELIMITER' and token['value'] == '(':
            self._consume_token(state)  # 消费 '('
            expr = self._parse_additive_expression(state)

            # 消费 ')'
            token = self._peek_token(state)
            if token and token['type'] == 'DELIMITER' and token['value'] == ')':
                self._consume_token(state)

            return expr

        # 数字字面量
        if token['type'] == 'NUMBER':
            token = self._consume_token(state)
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=token['value'],
                data_type=DataType.FLOAT if 'f' in token['value'] or '.' in token['value'] else DataType.INT,
                children=[],
                line_number=state['line_number']
            )

        # 标识符 (变量，包括成员访问)
        if token['type'] == 'IDENTIFIER':
            token = self._consume_token(state)

            # 推断类型
            if '.' in token['value']:
                # 成员访问 - 推断结果类型
                data_type = self.type_engine.infer_member_access_type(token['value'])
                node_type = NodeType.MEMBER_ACCESS
            else:
                # 普通变量
                data_type = DataType.UNKNOWN  # 类型默认为 UNKNOWN
                node_type = NodeType.VARIABLE

            return ASTNode(
                node_type=node_type,
                value=token['value'],
                data_type=data_type,
                children=[],
                line_number=state['line_number']
            )

        # 无法识别的token
        return None

    def _parse_primary(self, expr: str, line_number: int) -> ASTNode:
        """解析基本元素（变量、字面量、函数调用等） - 只构建结构，不推断类型"""
        expr = expr.strip()

        # 处理括号表达式 - 去掉外层括号并递归解析
        if expr.startswith('(') and expr.endswith(')'):
            # 检查括号是否匹配（确保是完整的外层括号）
            paren_count = 0
            for i, char in enumerate(expr):
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                    if paren_count == 0 and i < len(expr) - 1:
                        # 括号在中间就关闭了，不是外层括号
                        break
            else:
                # 如果循环正常结束，说明是完整的外层括号
                if paren_count == 0:
                    inner_expr = expr[1:-1].strip()
                    return self._parse_expression(inner_expr, line_number)

        # 函数调用 - 支持命名空间（如 fast::clamp）
        func_match = re.match(r'([a-zA-Z_][a-zA-Z0-9_]*(?:::[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\((.*)\)$', expr)
        if func_match:
            func_name = func_match.group(1)
            args_str = func_match.group(2)

            func_node = ASTNode(
                node_type=NodeType.FUNCTION,
                value=func_name,
                data_type=DataType.UNKNOWN,  # 不在语法树阶段推断类型
                children=[],
                line_number=line_number
            )

            # 解析参数
            if args_str.strip():
                args = self._split_arguments(args_str)
                for arg in args:
                    arg_node = self._parse_expression(arg, line_number)
                    func_node.add_child(arg_node)

            return func_node

        # 数字字面量 - 必须在成员访问之前检测
        if re.match(r'^-?\d+(\.\d+)?f?$', expr):
            return ASTNode(
                node_type=NodeType.LITERAL,
                value=expr,
                data_type=DataType.FLOAT if 'f' in expr or '.' in expr else DataType.INT,
                children=[],
                line_number=line_number
            )

        # 成员访问 - 不进行类型推断，当作完整标识符处理
        if '.' in expr and re.match(r'^[a-zA-Z_][a-zA-Z0-9_.]*$', expr):
            member_node = ASTNode(
                node_type=NodeType.MEMBER_ACCESS,
                value=expr,
                data_type=DataType.UNKNOWN,  # 不在语法树阶段推断类型
                children=[],
                line_number=line_number
            )
            return member_node

        # 变量
        if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', expr):
            return ASTNode(
                node_type=NodeType.VARIABLE,
                value=expr,
                data_type=DataType.UNKNOWN,
                children=[],
                line_number=line_number
            )

        # 默认情况 - 如果无法识别，尝试作为复杂表达式解析
        # 这种情况通常不应该发生，可能表示解析逻辑有问题
        print(f"警告: 无法识别的表达式 '{expr}'，尝试作为变量处理")
        return ASTNode(
            node_type=NodeType.VARIABLE,
            value=expr,
            data_type=DataType.UNKNOWN,
            children=[],
            line_number=line_number
        )

    def _split_arguments(self, args_str: str) -> List[str]:
        """分割函数参数"""
        args = []
        current_arg = ""
        paren_level = 0

        for char in args_str:
            if char == ',' and paren_level == 0:
                args.append(current_arg.strip())
                current_arg = ""
            else:
                if char == '(':
                    paren_level += 1
                elif char == ')':
                    paren_level -= 1
                current_arg += char

        if current_arg.strip():
            args.append(current_arg.strip())

        return args

    def print_syntax_tree(self, syntax_tree: ASTNode, line_number: int, line_content: str):
        """将语法树结构输出到日志文件"""
        log_file_path = "syntax_tree_log.txt"

        with open(log_file_path, "a", encoding="utf-8") as f:
            if syntax_tree is None:
                f.write(f"第{line_number}行: {line_content}\n")
                f.write("  语法树为空\n")
                f.write("-" * 50 + "\n")
                return

            f.write(f"第{line_number}行: {line_content}\n")
            f.write("语法树结构:\n")
            f.write(syntax_tree.print_tree())
            f.write("-" * 50 + "\n")

    def print_operation_process(self, operation_process: List[operationProcess], line_number: int, line_content: str):
        """将运算过程输出到日志文件"""
        log_file_path = "operation_process_log.txt"

        with open(log_file_path, "a", encoding="utf-8") as f:
            f.write(f"第{line_number}行运算过程: {line_content}\n")

            if not operation_process:
                f.write("  无运算过程\n")
                f.write("=" * 50 + "\n")
                return

            f.write("运算过程详情:\n")
            for i, op in enumerate(operation_process, 1):
                f.write(f"  步骤{i}: {op.string}\n")

                # 写入左侧数据类型（结果类型）
                if op.left_dataType:
                    left_types = [dt.value for dt in op.left_dataType]
                    f.write(f"    结果类型: {', '.join(left_types)}\n")

                # 写入右侧数据类型（操作数类型）
                if op.right_dataType:
                    right_types = [dt.value for dt in op.right_dataType]
                    f.write(f"    操作数类型: {', '.join(right_types)}\n")

                f.write("\n")  # 空行分隔

            f.write("=" * 50 + "\n")







###########################提取变量信息############################################
    def _extract_variable_types(self, node: ASTNode, variable_types: Dict[str, DataType], global_var_types: Dict[str, DataType]):
        """遍历语法树并推断核心节点的类型（变量、字面量、成员访问）"""
        if node is None:
            return

        # 先递归处理子节点，确保子节点的类型已经推断
        for child in node.children:
            self._extract_variable_types(child, variable_types, global_var_types)

        # 只推断三种核心类型的节点
        if node.node_type == NodeType.VARIABLE:
            # 变量引用 - 从全局类型表中查找
            if node.value in global_var_types:
                node.data_type = global_var_types[node.value]
                variable_types[node.value] = node.data_type
            else:
                # 如果找不到，尝试通过类型推断引擎推断
                inferred_type = self.type_engine.infer_variable_type(node.value)
                node.data_type = inferred_type
                if inferred_type != DataType.UNKNOWN:
                    variable_types[node.value] = inferred_type
                    global_var_types[node.value] = inferred_type

        elif node.node_type == NodeType.MEMBER_ACCESS:
            # 成员访问 - 使用类型推断引擎
            inferred_type = self.type_engine.infer_member_access_type(node.value)
            node.data_type = inferred_type

        elif node.node_type == NodeType.LITERAL:
            # 字面量 - 根据值推断类型
            inferred_type = self._infer_literal_type(node.value)
            node.data_type = inferred_type

    def _infer_literal_type(self, value: str) -> DataType:
        """从字面量值推断类型"""
        # 整数字面量
        if re.match(r'^-?\d+$', value):
            return DataType.INT
        # 浮点数字面量
        elif re.match(r'^-?\d*\.\d+f?$', value) or re.match(r'^-?\d+\.\d*f?$', value):
            if value.endswith('f'):
                return DataType.FLOAT
            else:
                return DataType.DOUBLE
        # 布尔字面量
        elif value in ['true', 'false']:
            return DataType.BOOL
        # 字符串字面量
        elif value.startswith('"') and value.endswith('"'):
            return DataType.UNKNOWN  # 着色器中通常没有字符串类型
        else:
            return DataType.UNKNOWN












###########################生成运算过程############################################
    def _generate_operation_process(self, node: ASTNode, global_var_types: Dict[str, DataType]) -> List[operationProcess]:
        """生成运算过程"""
        operations = []
        self._reset_temp_var_info()
        if node is None:
            # 如果节点为空，返回空的操作列表
            return operations

        if node.node_type == NodeType.ASSIGNMENT:
            # 处理赋值语句
            left_node = node.children[0]
            right_node = node.children[1]

            # 生成右侧表达式的运算过程
            right_operations, right_result_var = self._generate_expression_operations(right_node, global_var_types)
            operations.extend(right_operations)

            # 生成最终赋值操作
            final_op = operationProcess()
            final_op.string = f"{left_node.value} = {right_result_var}"
            final_op.left_dataType = [left_node.data_type] if left_node.data_type != DataType.UNKNOWN else []


            right_type = self._get_node_type(right_result_var,right_node, global_var_types)
            final_op.right_dataType = [right_type]

            operations.append(final_op)
        elif node.node_type == NodeType.OPERATOR:
            # 处理控制流语句，不生成运算过程
            # 控制流语句（如 for、while、if 等）不需要生成运算过程
            pass
        else:
            # 其他类型的节点，暂时不处理
            pass

        return operations

    def _reset_temp_var_info(self):
        self.temp_var_counter = 0
        self.temp_var_types = {}

    
    def _generate_expression_operations(self, node: ASTNode, global_var_types: Dict[str, DataType]) -> Tuple[List[operationProcess], str]:
        """生成表达式的运算过程，返回操作列表和结果变量名"""
        operations = []

        if node.node_type == NodeType.OPERATOR:
            # 处理运算符
            if len(node.children) == 2:
                # 二元运算符
                left_child = node.children[0]
                right_child = node.children[1]

                # 生成左操作数的运算过程
                left_ops, left_var = self._generate_expression_operations(left_child, global_var_types)
                operations.extend(left_ops)

                # 生成右操作数的运算过程
                right_ops, right_var = self._generate_expression_operations(right_child, global_var_types)
                operations.extend(right_ops)

                # 生成当前运算
                temp_var = f"tmp_{self.temp_var_counter}"
                self.temp_var_counter += 1

                # 获取操作数类型
                left_type = self._get_node_type(left_var,left_child, global_var_types)
                right_type = self._get_node_type(right_var,right_child, global_var_types)
            
            
                # 推断运算结果类型
                result_type = self._infer_operation_result_type(left_type, right_type, node.value)
                self.temp_var_types[temp_var] = result_type

                op = operationProcess()
                op.string = f"{temp_var} = {left_var} {node.value} {right_var}"
                op.left_dataType = [result_type]  # 左侧是运算结果类型
                op.right_dataType = [left_type, right_type]  # 右侧是两个操作数的类型
                operations.append(op)

                return operations, temp_var
            elif len(node.children) == 1:
                # 一元运算符
                child = node.children[0]
                child_ops, child_var = self._generate_expression_operations(child, global_var_types)
                operations.extend(child_ops)

                temp_var = f"tmp_{self.temp_var_counter}"
                self.temp_var_counter += 1
                # 获取操作数类型
                child_type = self._get_node_type(child_var, child, global_var_types)
    
                # 一元运算符的结果类型推断
                result_type = self._infer_unary_operation_result_type(child_type, node.value)
                self.temp_var_types[temp_var] = result_type

                op = operationProcess()
                op.string = f"{temp_var} = {node.value}{child_var}"
                op.left_dataType = [result_type]  # 左侧是一元运算的结果类型
                op.right_dataType = [child_type]
                operations.append(op)

                return operations, temp_var

        elif node.node_type == NodeType.FUNCTION:
            # 处理函数调用
            arg_vars = []
            arg_types = []
            for child in node.children:
                child_ops, child_var = self._generate_expression_operations(child, global_var_types)
                operations.extend(child_ops)
                arg_vars.append(child_var)

                child_type = self._get_node_type(child_var, child, global_var_types)
                arg_types.append(child_type)

            temp_var = f"tmp_{self.temp_var_counter}"
            self.temp_var_counter += 1

            # 推断函数返回类型
            result_type = self._infer_function_result_type(node.value, arg_types)
            self.temp_var_types[temp_var] = result_type

            args_str = ", ".join(arg_vars)
            op = operationProcess()
            op.string = f"{temp_var} = {node.value}({args_str})"
            op.left_dataType = [result_type]  
            op.right_dataType = arg_types
            operations.append(op)

            return operations, temp_var

        elif node.node_type == NodeType.MEMBER_ACCESS:
            # 处理成员访问（如 vec.xy）
            temp_var = f"tmp_{self.temp_var_counter}"
            self.temp_var_counter += 1

            # 使用类型推断引擎推断成员访问类型
            result_type = self.type_engine.infer_member_access_type(node.value)
            self.temp_var_types[temp_var] = result_type

            op = operationProcess()
            op.string = f"{temp_var} = {node.value}"
            op.left_dataType = [result_type]
            op.right_dataType = [result_type]
            operations.append(op)

            return operations, temp_var

        # 基本元素（变量、字面量等）
        # 对于字面量和变量，不需要生成额外的运算操作
        return operations, node.value

    def _get_node_type(self, value: str, node: ASTNode, global_var_types: Dict[str, DataType]) -> DataType:
        """获取节点的数据类型 - 使用type_engine进行推断"""
        # 优先通过名字直接返回,如果名字不行就再用节点
        if value in global_var_types:
            return global_var_types[value]
        elif value in self.temp_var_types:
            return self.temp_var_types[value]
        
        if node.node_type == NodeType.MEMBER_ACCESS:
            # 使用type_engine推断成员访问类型
            return self.type_engine.infer_member_access_type(node.value)
        
        if node.data_type != DataType.UNKNOWN:
            return node.data_type
        return DataType.UNKNOWN


    def _infer_operation_result_type(self, left_type: DataType, right_type: DataType, operator: str) -> DataType:
        """推断运算结果的类型"""
        # 如果有未知类型，返回另一个类型
        if left_type == DataType.UNKNOWN:
            return right_type
        if right_type == DataType.UNKNOWN:
            return left_type

        # 算术运算的类型提升规则
        if operator in ['+', '-', '*', '/']:
            # float 优先于 half
            if left_type == DataType.FLOAT or right_type == DataType.FLOAT:
                return DataType.FLOAT
            if left_type == DataType.HALF or right_type == DataType.HALF:
                return DataType.HALF

            # 向量类型处理
            vector_types = [DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4,
                           DataType.HALF2, DataType.HALF3, DataType.HALF4]
            if left_type in vector_types:
                return left_type
            if right_type in vector_types:
                return right_type

            # 整数类型
            if left_type == DataType.INT or right_type == DataType.INT:
                return DataType.INT

        # 比较运算返回bool
        if operator in ['==', '!=', '<', '<=', '>', '>=']:
            return DataType.BOOL

        # 逻辑运算返回bool
        if operator in ['&&', '||']:
            return DataType.BOOL


    def _infer_unary_operation_result_type(self, operand_type: DataType, operator: str) -> DataType:
        """推断一元运算结果的类型"""
        # 大多数一元运算符保持操作数的类型
        if operator in ['+', '-', '!', '~']:
            return operand_type

        # 默认返回操作数类型
        return operand_type

    def _infer_function_result_type(self, func_name: str, arg_types: List[DataType]) -> DataType:
        """推断函数返回类型"""
        return self.type_engine.infer_function_call_type(func_name, arg_types)








###########################检查类型转换############################################
    def _check_type_conversions(self, operations: List[operationProcess]) -> List[Dict]:
        """检查类型转换"""
        conversions = []

        for op in operations:
            # 检查左右操作数类型是否匹配
            if op.left_dataType and op.right_dataType:
                for left_type in op.left_dataType:
                    for right_type in op.right_dataType:
                        if left_type != right_type and left_type != DataType.UNKNOWN and right_type != DataType.UNKNOWN:
                            # 发现类型转换
                            conversion = {
                                'operation': op.string,
                                'from_type': right_type.value,
                                'to_type': left_type.value,
                                'conversion_type': self._get_conversion_type(right_type, left_type)
                            }
                            conversions.append(conversion)

        return conversions

    def _get_conversion_type(self, from_type: DataType, to_type: DataType) -> str:
        """获取转换类型"""
        # 精度转换
        if from_type == DataType.FLOAT and to_type == DataType.HALF:
            return "precision_loss"
        elif from_type == DataType.HALF and to_type == DataType.FLOAT:
            return "precision_gain"

        # 向量转换
        vector_types = [DataType.FLOAT2, DataType.FLOAT3, DataType.FLOAT4,
                       DataType.HALF2, DataType.HALF3, DataType.HALF4]
        if from_type in vector_types and to_type in vector_types:
            return "vector_conversion"

        # 标量到向量
        scalar_types = [DataType.FLOAT, DataType.HALF, DataType.INT, DataType.UINT]
        if from_type in scalar_types and to_type in vector_types:
            return "scalar_to_vector"

        # 向量到标量
        if from_type in vector_types and to_type in scalar_types:
            return "vector_to_scalar"

        return "implicit_conversion"








class SyntaxTreeAnalyzer:
    """语法树分析器 - 整合代码行分析和语法树构建"""

    def __init__(self):
        self.tree_builder = SyntaxTreeBuilder()
        self.global_var_types: Dict[str, DataType] = {}
        # 使用全局类型推断引擎
        self.type_engine = type_inference_engine

    def analyze_shader_with_syntax_trees(self, shader_content: str) -> Dict[str, Any]:
        """分析着色器代码并构建语法树"""
        # 使用类型推断引擎解析着色器内容，获取结构体和函数定义
        self.type_engine.parse_shader_content(shader_content)
    
        # 使用代码行分析器获取有效代码行
        line_analyzer = CodeLineAnalyzer()
        code_lines = line_analyzer.analyze_shader_code(shader_content)

        # 分析每一行代码
        line_analyses = []
        for code_line in code_lines:
            line_analysis = self.tree_builder.analyze_line(
                code_line.content,
                code_line.line_number,
                self.global_var_types
            )
            line_analyses.append(line_analysis)

        # 整合结果
        result = {
            'code_lines': code_lines,
            'line_analyses': line_analyses,
            'global_type_table': self.global_var_types.copy(),
            'struct_definitions': self.type_engine.type_registry.copy(),
            'variable_types': self.type_engine.variable_types.copy()
        }

        return result
