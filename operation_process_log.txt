=== 运算过程分析日志 ===

第74行运算过程: constant half3 _18526 = {};
运算过程详情:
  步骤1: _18526 = {}
    操作数类型: unknown

==================================================
第75行运算过程: constant float3 _19185 = {};
运算过程详情:
  步骤1: _19185 = {}
    操作数类型: unknown

==================================================
第76行运算过程: constant half _19493 = {};
运算过程详情:
  步骤1: _19493 = {}
    操作数类型: unknown

==================================================
第77行运算过程: constant float _19585 = {};
运算过程详情:
  步骤1: _19585 = {}
    操作数类型: unknown

==================================================
第78行运算过程: constant int _19621 = {};
运算过程详情:
  步骤1: _19621 = {}
    操作数类型: unknown

==================================================
第79行运算过程: constant float3 _21234 = {};
运算过程详情:
  步骤1: _21234 = {}
    操作数类型: unknown

==================================================
第80行运算过程: constant float3 _21295 = {};
运算过程详情:
  步骤1: _21295 = {}
    操作数类型: unknown

==================================================
第81行运算过程: constant half4 _21296 = {};
运算过程详情:
  步骤1: _21296 = {}
    操作数类型: unknown

==================================================
第82行运算过程: constant half3 _21297 = {};
运算过程详情:
  步骤1: _21297 = {}
    操作数类型: unknown

==================================================
第105行运算过程: main0_out out = {};
运算过程详情:
  步骤1: out = {}
    操作数类型: unknown

==================================================
第106行运算过程: half _9176 = half(0);
运算过程详情:
  步骤1: tmp_0 = half(0)
    结果类型: half
    操作数类型: int

  步骤2: _9176 = tmp_0
    操作数类型: half

==================================================
第108行运算过程: half _9199 = half(1);
运算过程详情:
  步骤1: tmp_0 = half(1)
    结果类型: half
    操作数类型: int

  步骤2: _9199 = tmp_0
    操作数类型: half

==================================================
第109行运算过程: float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: tmp_3 = fast::normalize(tmp_2)
    结果类型: float3
    操作数类型: float3

  步骤5: _8921 = tmp_3
    操作数类型: float3

==================================================
第110行运算过程: float3 _8925 = float3(in.IN_WorldNormal.xyz);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldNormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: _8925 = tmp_1
    操作数类型: float3

==================================================
第113行运算过程: half3 _8941 = _8939.xyz;
运算过程详情:
  步骤1: tmp_0 = _8939.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: _8941 = tmp_0
    操作数类型: half3

==================================================
第119行运算过程: float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = in.IN_TintColor.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: double

  步骤3: tmp_2 = max(tmp_0, tmp_1)
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: tmp_4 = powr(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = fast::clamp(tmp_4)
    结果类型: float
    操作数类型: float

  步骤7: _8991 = tmp_5
    操作数类型: float

==================================================
第120行运算过程: half _8994 = _8974.x;
运算过程详情:
  步骤1: tmp_0 = _8974.x
    结果类型: half
    操作数类型: half

  步骤2: _8994 = tmp_0
    操作数类型: half

==================================================
第121行运算过程: half _8996 = _8983.x;
运算过程详情:
  步骤1: tmp_0 = _8983.x
    结果类型: half
    操作数类型: half

  步骤2: _8996 = tmp_0
    操作数类型: half

==================================================
第123行运算过程: half _9019 = half(1.0);
运算过程详情:
  步骤1: tmp_0 = half(1.0)
    结果类型: half
    操作数类型: double

  步骤2: _9019 = tmp_0
    操作数类型: half

==================================================
第124行运算过程: if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)
  无运算过程
==================================================
第126行运算过程: discard_fragment();
  无运算过程
==================================================
第129行运算过程: half _9251 = half(2);
运算过程详情:
  步骤1: tmp_0 = half(2)
    结果类型: half
    操作数类型: int

  步骤2: _9251 = tmp_0
    操作数类型: half

==================================================
第132行运算过程: half _9257 = _9255.x;
运算过程详情:
  步骤1: tmp_0 = _9255.x
    结果类型: half
    操作数类型: half

  步骤2: _9257 = tmp_0
    操作数类型: half

==================================================
第133行运算过程: half _9263 = _9255.y;
运算过程详情:
  步骤1: tmp_0 = _9255.y
    结果类型: half
    操作数类型: half

  步骤2: _9263 = tmp_0
    操作数类型: half

==================================================
第134行运算过程: half _9270 = _9255.z;
运算过程详情:
  步骤1: tmp_0 = _9255.z
    结果类型: half
    操作数类型: half

  步骤2: _9270 = tmp_0
    操作数类型: half

==================================================
第136行运算过程: float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
运算过程详情:
  步骤1: tmp_0 = in.IN_StaticWorldNormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: _9286 = tmp_1
    操作数类型: float3

==================================================
第141行运算过程: half _9079 = _9074.y;
运算过程详情:
  步骤1: tmp_0 = _9074.y
    结果类型: half
    操作数类型: half

  步骤2: _9079 = tmp_0
    操作数类型: half

==================================================
第148行运算过程: if (!gl_FrontFacing)
  无运算过程
==================================================
第157行运算过程: float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = _Block1.cCIFadeTime.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.cCIFadeTime.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _Block1.CameraPos.w
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = _Block1.cCIFadeTime.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = tmp_2 - tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _Block1.cCIFadeTime.z
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = tmp_4 * tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = fast::clamp(tmp_6, 0.0, 1.0)
    结果类型: float
    操作数类型: float, double, double

  步骤9: tmp_8 = mix(tmp_0, tmp_1, tmp_7)
    结果类型: float
    操作数类型: float, float, float

  步骤10: _9408 = tmp_8
    操作数类型: float

==================================================
第169行运算过程: if (_Block1.cCISwitchData.x > 0.0)
  无运算过程
==================================================
第171行运算过程: float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));
运算过程详情:
  步骤1: tmp_0 = _Block1.EnvInfo.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::max(0.0, tmp_0)
    结果类型: float
    操作数类型: double, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: _9460 = tmp_3
    操作数类型: float

==================================================
第174行运算过程: float _9505 = _9443.y;
运算过程详情:
  步骤1: tmp_0 = _9443.y
    结果类型: float
    操作数类型: float

  步骤2: _9505 = tmp_0
    操作数类型: float

==================================================
第176行运算过程: float _9519 = float(half(9.9956989288330078125e-05));
运算过程详情:
  步骤1: tmp_0 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: double

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: _9519 = tmp_1
    操作数类型: float

==================================================
第197行运算过程: half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = _Block1.HexRenderOptionData
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float4
    操作数类型: double, float4

  步骤3: tmp_2 = fast::clamp(tmp_1)
    结果类型: float4
    操作数类型: float4

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float4

  步骤5: _8295 = tmp_3
    操作数类型: half

==================================================
第199行运算过程: half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));
运算过程详情:
  步骤1: tmp_0 = half(0.2125999927520751953125)
    结果类型: half
    操作数类型: double

  步骤2: tmp_1 = half(0.715200006961822509765625)
    结果类型: half
    操作数类型: double

  步骤3: tmp_2 = half(0.072200000286102294921875)
    结果类型: half
    操作数类型: double

  步骤4: tmp_3 = half3(tmp_0, tmp_1, tmp_2)
    结果类型: half3
    操作数类型: half, half, half

  步骤5: _8303 = tmp_3
    操作数类型: half3

==================================================
第204行运算过程: if (_Block1.eDynamicFresnelIntensity > 0.0)
  无运算过程
==================================================
第207行运算过程: float _9813 = abs(_Block1.eFresnelPower);
运算过程详情:
  步骤1: tmp_0 = _Block1.eFresnelPower
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: _9813 = tmp_1
    操作数类型: float

==================================================
第208行运算过程: float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));
运算过程详情:
  步骤1: tmp_0 = _Block1.eFresnelMinIntensity
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.eFresnelPower
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = fast::max(tmp_0, tmp_1)
    结果类型: float
    操作数类型: float, float

  步骤4: _9831 = tmp_2
    操作数类型: float

==================================================
第222行运算过程: _17899.z = _9926.z - _Block1.CSMShadowBiases.x;
运算过程详情:
  步骤1: tmp_0 = _9926.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _17899.z = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第223行运算过程: float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = float4(tmp_0, 1.0)
    结果类型: float4
    操作数类型: float3, double

  步骤3: _9942 = tmp_1
    操作数类型: float4

==================================================
第226行运算过程: _17902.z = _9945.z - _Block1.CSMShadowBiases.y;
运算过程详情:
  步骤1: tmp_0 = _9945.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _17902.z = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第228行运算过程: if (_Block1.CSMCacheIndexs.z > 0.0)
  无运算过程
==================================================
第231行运算过程: _9971.z = _9971.z - _Block1.CSMShadowBiases.z;
运算过程详情:
  步骤1: tmp_0 = _9971.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _9971.z = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第238行运算过程: float3 _10033 = _17902.xyz / float3(_9945.w);
运算过程详情:
  步骤1: tmp_0 = _17902.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _9945.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: _10033 = tmp_3
    操作数类型: float3

==================================================
第239行运算过程: float3 _10040 = _18237.xyz / float3(_18237.w);
运算过程详情:
  步骤1: tmp_0 = _18237.xyz
    结果类型: unknown
    操作数类型: unknown

  步骤2: tmp_1 = _18237.w
    结果类型: unknown
    操作数类型: unknown

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: unknown

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: unknown, float3

  步骤5: _10040 = tmp_3
    操作数类型: float3

==================================================
第243行运算过程: float _10113 = _21138.z;
运算过程详情:
  步骤1: tmp_0 = _21138.z
    结果类型: float
    操作数类型: float

  步骤2: _10113 = tmp_0
    操作数类型: float

==================================================
第244行运算过程: float2 _10120 = float2(_Block1.cShadowBias.w);
运算过程详情:
  步骤1: tmp_0 = _Block1.cShadowBias.w
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _10120 = tmp_1
    操作数类型: float2

==================================================
第252行运算过程: float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));
运算过程详情:
  步骤1: tmp_0 = _Block1.CSMCacheIndexs
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: float4

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: int

  步骤4: _10205 = tmp_2
    操作数类型: float

==================================================
第257行运算过程: float _10282 = _10177.x;
运算过程详情:
  步骤1: tmp_0 = _10177.x
    结果类型: float
    操作数类型: float

  步骤2: _10282 = tmp_0
    操作数类型: float

==================================================
第258行运算过程: float _10289 = _10181.x;
运算过程详情:
  步骤1: tmp_0 = _10181.x
    结果类型: float
    操作数类型: float

  步骤2: _10289 = tmp_0
    操作数类型: float

==================================================
第259行运算过程: float _10300 = _10181.y;
运算过程详情:
  步骤1: tmp_0 = _10181.y
    结果类型: float
    操作数类型: float

  步骤2: _10300 = tmp_0
    操作数类型: float

==================================================
第260行运算过程: float3 _9997 = _17899.xyz / float3(_9926.w);
运算过程详情:
  步骤1: tmp_0 = _17899.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _9926.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: _9997 = tmp_3
    操作数类型: float3

==================================================
第261行运算过程: float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
运算过程详情:
  步骤1: tmp_0 = half(10)
    结果类型: half
    操作数类型: int

  步骤2: tmp_1 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: double

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: tmp_4 = 1.0 - tmp_3
    结果类型: float
    操作数类型: double, float

  步骤6: tmp_5 = fast::min(tmp_4)
    结果类型: float
    操作数类型: float

  步骤7: _10004 = tmp_5
    操作数类型: float

==================================================
第271行运算过程: float _10416 = float(int(_Block1.CSMCacheIndexs.x));
运算过程详情:
  步骤1: tmp_0 = _Block1.CSMCacheIndexs.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: float

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: int

  步骤4: _10416 = tmp_2
    操作数类型: float

==================================================
第276行运算过程: float _10493 = _10388.x;
运算过程详情:
  步骤1: tmp_0 = _10388.x
    结果类型: float
    操作数类型: float

  步骤2: _10493 = tmp_0
    操作数类型: float

==================================================
第277行运算过程: float _10500 = _10392.x;
运算过程详情:
  步骤1: tmp_0 = _10392.x
    结果类型: float
    操作数类型: float

  步骤2: _10500 = tmp_0
    操作数类型: float

==================================================
第278行运算过程: float _10511 = _10392.y;
运算过程详情:
  步骤1: tmp_0 = _10392.y
    结果类型: float
    操作数类型: float

  步骤2: _10511 = tmp_0
    操作数类型: float

==================================================
第280行运算过程: float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _8370 = tmp_2
    操作数类型: float3

==================================================
第285行运算过程: float3 _10588 = float3(_Block1.EnvInfo.z);
运算过程详情:
  步骤1: tmp_0 = _Block1.EnvInfo.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float

  步骤3: _10588 = tmp_1
    操作数类型: float3

==================================================
第286行运算过程: half3 _8393 = half3(half(0.0));
运算过程详情:
  步骤1: tmp_0 = half(0.0)
    结果类型: half
    操作数类型: double

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: half

  步骤3: _8393 = tmp_1
    操作数类型: half3

==================================================
第290行运算过程: if (_8401)
  无运算过程
==================================================
第294行运算过程: if (_8401)
  无运算过程
==================================================
第297行运算过程: if ((_8397 & 8u) != 0u)
  无运算过程
==================================================
第302行运算过程: float _10797 = _10789.x;
运算过程详情:
  步骤1: tmp_0 = _10789.x
    结果类型: float
    操作数类型: float

  步骤2: _10797 = tmp_0
    操作数类型: float

==================================================
第307行运算过程: if ((_10797 - _10799) > 0.5)
  无运算过程
==================================================
第317行运算过程: float _21078 = _10789.y;
运算过程详情:
  步骤1: tmp_0 = _10789.y
    结果类型: float
    操作数类型: float

  步骤2: _21078 = tmp_0
    操作数类型: float

==================================================
第322行运算过程: if ((_21078 - _21079) > 0.5)
  无运算过程
==================================================
第332行运算过程: float _21100 = _10789.z;
运算过程详情:
  步骤1: tmp_0 = _10789.z
    结果类型: float
    操作数类型: float

  步骤2: _21100 = tmp_0
    操作数类型: float

==================================================
第337行运算过程: if ((_21100 - _21101) > 0.5)
  无运算过程
==================================================
第349行运算过程: if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))
  无运算过程
==================================================
第354行运算过程: if (_10704 <= 3u)
  无运算过程
==================================================
第357行运算过程: float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _10762.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float2
    操作数类型: float2, double

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float2
    操作数类型: float2, float2

  步骤7: _10994 = tmp_5
    操作数类型: float2

==================================================
第358行运算过程: float _11001 = _10822.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _11001 = tmp_1
    操作数类型: float

==================================================
第360行运算过程: float _11011 = _10822.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _11011 = tmp_1
    操作数类型: float

==================================================
第362行运算过程: float _11020 = _10994.x;
运算过程详情:
  步骤1: tmp_0 = _10994.x
    结果类型: float
    操作数类型: float

  步骤2: _11020 = tmp_0
    操作数类型: float

==================================================
第365行运算过程: float _11038 = _10994.y;
运算过程详情:
  步骤1: tmp_0 = _10994.y
    结果类型: float
    操作数类型: float

  步骤2: _11038 = tmp_0
    操作数类型: float

==================================================
第367行运算过程: float _11059 = (_10762.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _10762.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, double

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, double

  步骤4: _11059 = tmp_2
    操作数类型: float

==================================================
第373行运算过程: float _11100 = _11097.x;
运算过程详情:
  步骤1: tmp_0 = _11097.x
    结果类型: float
    操作数类型: float

  步骤2: _11100 = tmp_0
    操作数类型: float

==================================================
第380行运算过程: float _11138 = _11135.x;
运算过程详情:
  步骤1: tmp_0 = _11135.x
    结果类型: float
    操作数类型: float

  步骤2: _11138 = tmp_0
    操作数类型: float

==================================================
第391行运算过程: float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _10762.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float2
    操作数类型: float2, double

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float2
    操作数类型: float2, float2

  步骤7: _11233 = tmp_5
    操作数类型: float2

==================================================
第392行运算过程: float _11240 = _10822.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _11240 = tmp_1
    操作数类型: float

==================================================
第394行运算过程: float _11250 = _10822.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _11250 = tmp_1
    操作数类型: float

==================================================
第396行运算过程: float _11259 = _11233.x;
运算过程详情:
  步骤1: tmp_0 = _11233.x
    结果类型: float
    操作数类型: float

  步骤2: _11259 = tmp_0
    操作数类型: float

==================================================
第399行运算过程: float _11277 = _11233.y;
运算过程详情:
  步骤1: tmp_0 = _11233.y
    结果类型: float
    操作数类型: float

  步骤2: _11277 = tmp_0
    操作数类型: float

==================================================
第401行运算过程: float _11298 = (_10762.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _10762.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, double

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, double

  步骤4: _11298 = tmp_2
    操作数类型: float

==================================================
第414行运算过程: if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))
  无运算过程
==================================================
第434行运算过程: float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;
运算过程详情:
  步骤1: tmp_0 = _Block1.SHAOParam.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.SHAOParam.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _11467 = tmp_2
    操作数类型: float

==================================================
第444行运算过程: if (!((_8397 & 64u) > 0u))
  无运算过程
==================================================
第459行运算过程: float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.SunDirection.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _Block1.SunDirection.y
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = fast::min(tmp_2, 1.0)
    结果类型: float
    操作数类型: float, double

  步骤5: tmp_4 = _Block1.SunDirection.z
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = float3(tmp_1, tmp_3, tmp_4)
    结果类型: float3
    操作数类型: float, float, float

  步骤7: tmp_6 = fast::normalize(tmp_5)
    结果类型: float3
    操作数类型: float3

  步骤8: tmp_7 = tmp_6 * 200000.0
    结果类型: float3
    操作数类型: float3, double

  步骤9: tmp_8 = tmp_0 + tmp_7
    结果类型: float3
    操作数类型: float3, float3

  步骤10: _11600 = tmp_8
    操作数类型: float3

==================================================
第472行运算过程: half4 _11959 = half4(half(0.60000002384185791015625));
运算过程详情:
  步骤1: tmp_0 = half(0.60000002384185791015625)
    结果类型: half
    操作数类型: double

  步骤2: tmp_1 = half4(tmp_0)
    结果类型: half4
    操作数类型: half

  步骤3: _11959 = tmp_1
    操作数类型: half4

==================================================
第477行运算过程: half4 _11982 = half4(half(1.5));
运算过程详情:
  步骤1: tmp_0 = half(1.5)
    结果类型: half
    操作数类型: double

  步骤2: tmp_1 = half4(tmp_0)
    结果类型: half4
    操作数类型: half

  步骤3: _11982 = tmp_1
    操作数类型: half4

==================================================
第480行运算过程: half _11764 = half(0.699999988079071044921875);
运算过程详情:
  步骤1: tmp_0 = half(0.699999988079071044921875)
    结果类型: half
    操作数类型: double

  步骤2: _11764 = tmp_0
    操作数类型: half

==================================================
第486行运算过程: half _11815 = half(10.0);
运算过程详情:
  步骤1: tmp_0 = half(10.0)
    结果类型: half
    操作数类型: double

  步骤2: _11815 = tmp_0
    操作数类型: half

==================================================
第493行运算过程: float _12108 = float(half(9.9956989288330078125e-05));
运算过程详情:
  步骤1: tmp_0 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: double

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: _12108 = tmp_1
    操作数类型: float

==================================================
第497行运算过程: uint _12171 = uint(_Block1.LightDataBuffer[0].x);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = uint(tmp_0)
    结果类型: uint
    操作数类型: float4

  步骤3: _12171 = tmp_1
    操作数类型: uint

==================================================
第532行运算过程: if (!((_12298 & 2097152u) == 2097152u))
  无运算过程
==================================================
第555行运算过程: if (_12309 == 196608u)
  无运算过程
==================================================
第557行运算过程: float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = -tmp_0
    结果类型: float4
    操作数类型: float4

  步骤3: _12360 = tmp_1
    操作数类型: float4

==================================================
第558行运算过程: float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float3
    操作数类型: float3, float4

  步骤4: _12378 = tmp_2
    操作数类型: float3

==================================================
第561行运算过程: if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))
  无运算过程
==================================================
第592行运算过程: if (_12309 == 0u)
  无运算过程
==================================================
第596行运算过程: float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: _12599 = tmp_0
    操作数类型: float4

==================================================
第603行运算过程: if ((_12298 & 16777216u) == 16777216u)
  无运算过程
==================================================
第607行运算过程: if (_12858 > 0.00999999977648258209228515625)
  无运算过程
==================================================
第621行运算过程: float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = dot(tmp_0)
    结果类型: float
    操作数类型: float4

  步骤3: tmp_2 = fast::clamp(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: _12677 = tmp_2
    操作数类型: float

==================================================
第641行运算过程: if (_12309 == 65536u)
  无运算过程
==================================================
第644行运算过程: float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: _12933 = tmp_0
    操作数类型: float4

==================================================
第652行运算过程: if (_13202 > 0.00999999977648258209228515625)
  无运算过程
==================================================
第677行运算过程: if (_13270)
  无运算过程
==================================================
第679行运算过程: float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: _13339 = tmp_0
    操作数类型: float4

==================================================
第685行运算过程: float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float4

  步骤3: _13466 = tmp_1
    操作数类型: float3

==================================================
第687行运算过程: float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float4

  步骤3: _13472 = tmp_1
    操作数类型: float3

==================================================
第707行运算过程: float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = cross(tmp_0)
    结果类型: float3
    操作数类型: float4

  步骤3: tmp_2 = dot(tmp_1)
    结果类型: float
    操作数类型: float3

  步骤4: tmp_3 = step(0.0, tmp_2)
    结果类型: float
    操作数类型: double, float

  步骤5: _13539 = tmp_3
    操作数类型: float

==================================================
第762行运算过程: if (_19609 >= 1)
  无运算过程
==================================================
第773行运算过程: break; // unreachable workaround
  无运算过程
==================================================
第774行运算过程: } while(false);
  无运算过程
==================================================
第791行运算过程: if (_8573)
  无运算过程
==================================================
第796行运算过程: if (_8573)
  无运算过程
==================================================
第800行运算过程: float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraInfo.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = in.IN_LinearZ
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = _Block1.SHGIParam2.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = fast::max(tmp_3, 0.001000000047497451305389404296875)
    结果类型: float
    操作数类型: float, double

  步骤6: tmp_5 = tmp_2 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, double, double

  步骤8: _14482 = tmp_6
    操作数类型: float

==================================================
第803行运算过程: if ((_8397 & 2048u) != 0u)
  无运算过程
==================================================
第806行运算过程: float _14692 = _14684.x;
运算过程详情:
  步骤1: tmp_0 = _14684.x
    结果类型: float
    操作数类型: float

  步骤2: _14692 = tmp_0
    操作数类型: float

==================================================
第811行运算过程: if ((_14692 - _14694) > 0.5)
  无运算过程
==================================================
第821行运算过程: float _21034 = _14684.y;
运算过程详情:
  步骤1: tmp_0 = _14684.y
    结果类型: float
    操作数类型: float

  步骤2: _21034 = tmp_0
    操作数类型: float

==================================================
第826行运算过程: if ((_21034 - _21035) > 0.5)
  无运算过程
==================================================
第836行运算过程: float _21056 = _14684.z;
运算过程详情:
  步骤1: tmp_0 = _14684.z
    结果类型: float
    操作数类型: float

  步骤2: _21056 = tmp_0
    操作数类型: float

==================================================
第841行运算过程: if ((_21056 - _21057) > 0.5)
  无运算过程
==================================================
第854行运算过程: if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))
  无运算过程
==================================================
第866行运算过程: if (3 >= int(_14469))
  无运算过程
==================================================
第871行运算过程: float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _14747.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float2
    操作数类型: float2, double

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float2
    操作数类型: float2, float2

  步骤7: _14956 = tmp_5
    操作数类型: float2

==================================================
第872行运算过程: float _14963 = _14717.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _14963 = tmp_1
    操作数类型: float

==================================================
第874行运算过程: float _14973 = _14717.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _14973 = tmp_1
    操作数类型: float

==================================================
第876行运算过程: float _14982 = _14956.x;
运算过程详情:
  步骤1: tmp_0 = _14956.x
    结果类型: float
    操作数类型: float

  步骤2: _14982 = tmp_0
    操作数类型: float

==================================================
第879行运算过程: float _15000 = _14956.y;
运算过程详情:
  步骤1: tmp_0 = _14956.y
    结果类型: float
    操作数类型: float

  步骤2: _15000 = tmp_0
    操作数类型: float

==================================================
第881行运算过程: float _15021 = (_14747.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _14747.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, double

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, double

  步骤4: _15021 = tmp_2
    操作数类型: float

==================================================
第888行运算过程: float _15078 = _15059.x;
运算过程详情:
  步骤1: tmp_0 = _15059.x
    结果类型: float
    操作数类型: float

  步骤2: _15078 = tmp_0
    操作数类型: float

==================================================
第894行运算过程: float _15104 = _15074.x;
运算过程详情:
  步骤1: tmp_0 = _15074.x
    结果类型: float
    操作数类型: float

  步骤2: _15104 = tmp_0
    操作数类型: float

==================================================
第924行运算过程: half _15255 = half(32.0);
运算过程详情:
  步骤1: tmp_0 = half(32.0)
    结果类型: half
    操作数类型: double

  步骤2: _15255 = tmp_0
    操作数类型: half

==================================================
第936行运算过程: if (_8590)
  无运算过程
==================================================
第949行运算过程: float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _14747.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float2
    操作数类型: float2, double

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: double

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float2
    操作数类型: float2, float2

  步骤7: _15427 = tmp_5
    操作数类型: float2

==================================================
第950行运算过程: float _15434 = _14717.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _15434 = tmp_1
    操作数类型: float

==================================================
第952行运算过程: float _15444 = _14717.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, double

  步骤3: _15444 = tmp_1
    操作数类型: float

==================================================
第954行运算过程: float _15453 = _15427.x;
运算过程详情:
  步骤1: tmp_0 = _15427.x
    结果类型: float
    操作数类型: float

  步骤2: _15453 = tmp_0
    操作数类型: float

==================================================
第957行运算过程: float _15471 = _15427.y;
运算过程详情:
  步骤1: tmp_0 = _15427.y
    结果类型: float
    操作数类型: float

  步骤2: _15471 = tmp_0
    操作数类型: float

==================================================
第959行运算过程: float _15492 = (_14747.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _14747.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, double

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, double

  步骤4: _15492 = tmp_2
    操作数类型: float

==================================================
第966行运算过程: float _15549 = _15530.x;
运算过程详情:
  步骤1: tmp_0 = _15530.x
    结果类型: float
    操作数类型: float

  步骤2: _15549 = tmp_0
    操作数类型: float

==================================================
第970行运算过程: float _15561 = _15545.x;
运算过程详情:
  步骤1: tmp_0 = _15545.x
    结果类型: float
    操作数类型: float

  步骤2: _15561 = tmp_0
    操作数类型: float

==================================================
第983行运算过程: half _15623 = half(32.0);
运算过程详情:
  步骤1: tmp_0 = half(32.0)
    结果类型: half
    操作数类型: double

  步骤2: _15623 = tmp_0
    操作数类型: half

==================================================
第996行运算过程: if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))
  无运算过程
==================================================
第998行运算过程: half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = _15664.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _15664.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _15664.z
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = fast::max(tmp_1, tmp_2)
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::max(tmp_0, tmp_3)
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::clamp(tmp_4, 0.0, 1.0)
    结果类型: float
    操作数类型: float, double, double

  步骤7: tmp_6 = 1.0 - tmp_5
    结果类型: float
    操作数类型: double, float

  步骤8: tmp_7 = half(tmp_6)
    结果类型: half
    操作数类型: float

  步骤9: tmp_8 = half3(tmp_7)
    结果类型: half3
    操作数类型: half

  步骤10: _14921 = tmp_8
    操作数类型: half3

==================================================
第1023行运算过程: half3 _14565 = half3(float3(0.0));
运算过程详情:
  步骤1: tmp_0 = float3(0.0)
    结果类型: float3
    操作数类型: double

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float3

  步骤3: _14565 = tmp_1
    操作数类型: half3

==================================================
第1025行运算过程: if (_8590)
  无运算过程
==================================================
第1030行运算过程: if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))
  无运算过程
==================================================
第1032行运算过程: float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;
运算过程详情:
  步骤1: tmp_0 = _9109.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, double, double

  步骤3: tmp_2 = tmp_1 * 0.75
    结果类型: float
    操作数类型: float, double

  步骤4: _14592 = tmp_2
    操作数类型: float

==================================================
第1075行运算过程: float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.cLocalVirtualLitPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.cVirtualLitParam.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_0 + tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _15920 = tmp_2
    操作数类型: float3

==================================================
第1087行运算过程: half _16335 = half(-1.023326873779296875);
运算过程详情:
  步骤1: tmp_0 = -1.023326873779296875
    结果类型: double
    操作数类型: double

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: double

  步骤3: _16335 = tmp_1
    操作数类型: half

==================================================
第1088行运算过程: half _16336 = half(1.023326873779296875);
运算过程详情:
  步骤1: tmp_0 = half(1.023326873779296875)
    结果类型: half
    操作数类型: double

  步骤2: _16336 = tmp_0
    操作数类型: half

==================================================
第1089行运算过程: half _16342 = _9064.y;
运算过程详情:
  步骤1: tmp_0 = _9064.y
    结果类型: half
    操作数类型: half

  步骤2: _16342 = tmp_0
    操作数类型: half

==================================================
第1090行运算过程: half _16351 = half(-0.858085691928863525390625);
运算过程详情:
  步骤1: tmp_0 = -0.858085691928863525390625
    结果类型: double
    操作数类型: double

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: double

  步骤3: _16351 = tmp_1
    操作数类型: half

==================================================
第1092行运算过程: half _16361 = _9064.z;
运算过程详情:
  步骤1: tmp_0 = _9064.z
    结果类型: half
    操作数类型: half

  步骤2: _16361 = tmp_0
    操作数类型: half

==================================================
第1093行运算过程: half _16369 = _9064.x;
运算过程详情:
  步骤1: tmp_0 = _9064.x
    结果类型: half
    操作数类型: half

  步骤2: _16369 = tmp_0
    操作数类型: half

==================================================
第1095行运算过程: half _16387 = half(-0.2477079927921295166015625);
运算过程详情:
  步骤1: tmp_0 = -0.2477079927921295166015625
    结果类型: double
    操作数类型: double

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: double

  步骤3: _16387 = tmp_1
    操作数类型: half

==================================================
第1097行运算过程: half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));
运算过程详情:
  步骤1: tmp_0 = _Block1.cSHCoefficients
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float4

  步骤3: _16279 = tmp_1
    操作数类型: half3

==================================================
第1100行运算过程: half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));
运算过程详情:
  步骤1: tmp_0 = -0.66364002227783203125
    结果类型: double
    操作数类型: double

  步骤2: tmp_1 = float3(0.081409998238086700439453125, 0.74361002445220947265625, tmp_0)
    结果类型: float3
    操作数类型: double, double, double

  步骤3: tmp_2 = half3(tmp_1)
    结果类型: half3
    操作数类型: float3

  步骤4: _16397 = tmp_2
    操作数类型: half3

==================================================
第1101行运算过程: half _16509 = _16397.y;
运算过程详情:
  步骤1: tmp_0 = _16397.y
    结果类型: half
    操作数类型: half

  步骤2: _16509 = tmp_0
    操作数类型: half

==================================================
第1102行运算过程: half _16528 = _16397.z;
运算过程详情:
  步骤1: tmp_0 = _16397.z
    结果类型: half
    操作数类型: half

  步骤2: _16528 = tmp_0
    操作数类型: half

==================================================
第1103行运算过程: half _16536 = _16397.x;
运算过程详情:
  步骤1: tmp_0 = _16397.x
    结果类型: half
    操作数类型: half

  步骤2: _16536 = tmp_0
    操作数类型: half

==================================================
第1111行运算过程: float _16645 = _8370.y;
运算过程详情:
  步骤1: tmp_0 = _8370.y
    结果类型: float
    操作数类型: float

  步骤2: _16645 = tmp_0
    操作数类型: float

==================================================
第1119行运算过程: float _16759 = fast::max(0.0, _16602.y);
运算过程详情:
  步骤1: tmp_0 = _16602.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::max(0.0, tmp_0)
    结果类型: float
    操作数类型: double, float

  步骤3: _16759 = tmp_1
    操作数类型: float

==================================================
第1125行运算过程: float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.cVisibilitySH
    结果类型: float4
    操作数类型: float4

  步骤2: _16862 = tmp_0
    操作数类型: float4

==================================================
第1128行运算过程: if (_Block1.eIsPlayerOverride < 0.5)
  无运算过程
==================================================
第1131行运算过程: if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)
  无运算过程
==================================================
第1135行运算过程: if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)
  无运算过程
==================================================
第1155行运算过程: float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);
运算过程详情:
  步骤1: tmp_0 = _19031.x
    结果类型: unknown
    操作数类型: unknown

  步骤2: tmp_1 = _19031.y
    结果类型: unknown
    操作数类型: unknown

  步骤3: tmp_2 = _19031.z
    结果类型: unknown
    操作数类型: unknown

  步骤4: tmp_3 = float4(0.0)
    结果类型: float4
    操作数类型: double

  步骤5: tmp_4 = float4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: float4
    操作数类型: unknown, unknown, unknown, float4

  步骤6: _8808 = tmp_4
    操作数类型: float4

==================================================
第1157行运算过程: float3 _8816 = fast::min(_8808.xyz, float3(10000.0));
运算过程详情:
  步骤1: tmp_0 = _8808.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = float3(10000.0)
    结果类型: float3
    操作数类型: double

  步骤3: tmp_2 = fast::min(tmp_0, tmp_1)
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _8816 = tmp_2
    操作数类型: float3

==================================================
第1158行运算过程: out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);
运算过程详情:
  步骤1: tmp_0 = _8816.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _8816.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _8816.z
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = _8808.w
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = float4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: float4
    操作数类型: float, float, float, float

  步骤6: out._Ret = tmp_4
    结果类型: float4
    操作数类型: float4

==================================================
第1159行运算过程: return out;
  无运算过程
==================================================
