=== 运算过程分析日志 ===

第74行运算过程: constant half3 _18526 = {};
运算过程详情:
  步骤1: _18526 = {}
    结果类型: half3
    操作数类型: unknown

==================================================
第75行运算过程: constant float3 _19185 = {};
运算过程详情:
  步骤1: _19185 = {}
    结果类型: float3
    操作数类型: unknown

==================================================
第76行运算过程: constant half _19493 = {};
运算过程详情:
  步骤1: _19493 = {}
    结果类型: half
    操作数类型: unknown

==================================================
第77行运算过程: constant float _19585 = {};
运算过程详情:
  步骤1: _19585 = {}
    结果类型: float
    操作数类型: unknown

==================================================
第78行运算过程: constant int _19621 = {};
运算过程详情:
  步骤1: _19621 = {}
    结果类型: int
    操作数类型: unknown

==================================================
第79行运算过程: constant float3 _21234 = {};
运算过程详情:
  步骤1: _21234 = {}
    结果类型: float3
    操作数类型: unknown

==================================================
第80行运算过程: constant float3 _21295 = {};
运算过程详情:
  步骤1: _21295 = {}
    结果类型: float3
    操作数类型: unknown

==================================================
第81行运算过程: constant half4 _21296 = {};
运算过程详情:
  步骤1: _21296 = {}
    结果类型: half4
    操作数类型: unknown

==================================================
第82行运算过程: constant half3 _21297 = {};
运算过程详情:
  步骤1: _21297 = {}
    结果类型: half3
    操作数类型: unknown

==================================================
第86行运算过程: float4 _Ret [[color(0)]];
  无运算过程
==================================================
第91行运算过程: float4 IN_TexCoord [[user(locn0)]];
  无运算过程
==================================================
第92行运算过程: float4 IN_WorldPosition [[user(locn1)]];
  无运算过程
==================================================
第93行运算过程: half4 IN_WorldNormal [[user(locn2)]];
  无运算过程
==================================================
第94行运算过程: half4 IN_WorldTangent [[user(locn3)]];
  无运算过程
==================================================
第95行运算过程: half4 IN_WorldBinormal [[user(locn4)]];
  无运算过程
==================================================
第96行运算过程: half4 IN_TintColor [[user(locn5)]];
  无运算过程
==================================================
第97行运算过程: float IN_LinearZ [[user(locn6)]];
  无运算过程
==================================================
第98行运算过程: half3 IN_LocalPosition [[user(locn7)]];
  无运算过程
==================================================
第99行运算过程: half4 IN_StaticWorldNormal [[user(locn8)]];
  无运算过程
==================================================
第104行运算过程: constexpr sampler sShadowMapArraySamplerSmplr(filter::linear, mip_filter::linear, compare_func::greater);
  无运算过程
==================================================
第105行运算过程: main0_out out = {};
运算过程详情:
  步骤1: out = {}
    操作数类型: unknown

==================================================
第106行运算过程: half _9176 = half(0);
运算过程详情:
  步骤1: tmp_0 = half(0)
    结果类型: half
    操作数类型: int

  步骤2: _9176 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第107行运算过程: half3 _9179 = half3(_9176);
运算过程详情:
  步骤1: tmp_0 = half3(_9176)
    结果类型: half3
    操作数类型: half

  步骤2: _9179 = tmp_0
    结果类型: half3
    操作数类型: half3

==================================================
第108行运算过程: half _9199 = half(1);
运算过程详情:
  步骤1: tmp_0 = half(1)
    结果类型: half
    操作数类型: int

  步骤2: _9199 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第109行运算过程: float3 _8921 = fast::normalize(_Block1.CameraPos.xyz - in.IN_WorldPosition.xyz);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: tmp_3 = fast::normalize(tmp_2)
    结果类型: float3
    操作数类型: float3

  步骤5: _8921 = tmp_3
    结果类型: float3
    操作数类型: float3

==================================================
第110行运算过程: float3 _8925 = float3(in.IN_WorldNormal.xyz);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldNormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: _8925 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第111行运算过程: half _8929 = half(fast::clamp(dot(_8925, _8921), 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = dot(_8925, _8921)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: _8929 = tmp_2
    结果类型: half
    操作数类型: half

==================================================
第112行运算过程: half4 _8939 = sBaseSampler.sample(sBaseSamplerSmplr, in.IN_TexCoord.xy);
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = sBaseSampler.sample(sBaseSamplerSmplr, tmp_0)
    结果类型: float
    操作数类型: unknown, float2

  步骤3: _8939 = tmp_1
    结果类型: half4
    操作数类型: float

==================================================
第113行运算过程: half3 _8941 = _8939.xyz;
运算过程详情:
  步骤1: tmp_0 = _8939.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: _8941 = tmp_0
    结果类型: half3
    操作数类型: half3

==================================================
第114行运算过程: half3 _8949 = half3(float3(_8941 * _8941) * float3(_Block1.cBaseColor));
运算过程详情:
  步骤1: tmp_0 = _8941 * _8941
    结果类型: half3
    操作数类型: half3, half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = _Block1.cBaseColor
    结果类型: float3
    操作数类型: float3

  步骤4: tmp_3 = float3(tmp_2)
    结果类型: float3
    操作数类型: float3

  步骤5: tmp_4 = tmp_1 * tmp_3
    结果类型: float3
    操作数类型: float3, float3

  步骤6: tmp_5 = half3(tmp_4)
    结果类型: half3
    操作数类型: float3

  步骤7: _8949 = tmp_5
    结果类型: half3
    操作数类型: half3

==================================================
第115行运算过程: float4 _8973 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise1Scale));
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _Block1.cNoise1Scale
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float2, float

  步骤4: tmp_3 = sNoiseSampler.sample(sNoiseSamplerSmplr, tmp_2)
    结果类型: float
    操作数类型: unknown, float

  步骤5: _8973 = tmp_3
    结果类型: float4
    操作数类型: float

==================================================
第116行运算过程: half4 _8974 = half4(_8973);
运算过程详情:
  步骤1: tmp_0 = half4(_8973)
    结果类型: half4
    操作数类型: float4

  步骤2: _8974 = tmp_0
    结果类型: half4
    操作数类型: half4

==================================================
第117行运算过程: float4 _8982 = sNoiseSampler.sample(sNoiseSamplerSmplr, (in.IN_TexCoord.xy * _Block1.cNoise2Scale));
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _Block1.cNoise2Scale
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float2, float

  步骤4: tmp_3 = sNoiseSampler.sample(sNoiseSamplerSmplr, tmp_2)
    结果类型: float
    操作数类型: unknown, float

  步骤5: _8982 = tmp_3
    结果类型: float4
    操作数类型: float

==================================================
第118行运算过程: half4 _8983 = half4(_8982);
运算过程详情:
  步骤1: tmp_0 = half4(_8982)
    结果类型: half4
    操作数类型: float4

  步骤2: _8983 = tmp_0
    结果类型: half4
    操作数类型: half4

==================================================
第119行运算过程: float _8991 = fast::clamp(powr(float(max(in.IN_TintColor.w, half(9.9956989288330078125e-05))), _Block1.cNoise2Bias), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = in.IN_TintColor.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = max(tmp_0, tmp_1)
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: tmp_4 = powr(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = fast::clamp(tmp_4)
    结果类型: float
    操作数类型: float

  步骤7: _8991 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第120行运算过程: half _8994 = _8974.x;
运算过程详情:
  步骤1: tmp_0 = _8974.x
    结果类型: half
    操作数类型: half

  步骤2: _8994 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第121行运算过程: half _8996 = _8983.x;
运算过程详情:
  步骤1: tmp_0 = _8983.x
    结果类型: half
    操作数类型: half

  步骤2: _8996 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第122行运算过程: float _9014 = 1.0 - _8991;
运算过程详情:
  步骤1: tmp_0 = 1.0 - _8991
    结果类型: float
    操作数类型: float, float

  步骤2: _9014 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第123行运算过程: half _9019 = half(1.0);
运算过程详情:
  步骤1: tmp_0 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤2: _9019 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第124行运算过程: if ((float((_8994 * _8996) - half(mix(0.0, 0.5, powr(float(max(_9199 - _8929, half(9.9956989288330078125e-05))), 3.0)))) - 0.100000001490116119384765625) < 0.0)
  无运算过程
==================================================
第126行运算过程: discard_fragment();
  无运算过程
==================================================
第128行运算过程: half4 _9248 = sNormalSampler.sample(sNormalSamplerSmplr, in.IN_TexCoord.xy);
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = sNormalSampler.sample(sNormalSamplerSmplr, tmp_0)
    结果类型: float
    操作数类型: unknown, float2

  步骤3: _9248 = tmp_1
    结果类型: half4
    操作数类型: float

==================================================
第129行运算过程: half _9251 = half(2);
运算过程详情:
  步骤1: tmp_0 = half(2)
    结果类型: half
    操作数类型: int

  步骤2: _9251 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第130行运算过程: half3 _9254 = half3(_9199);
运算过程详情:
  步骤1: tmp_0 = half3(_9199)
    结果类型: half3
    操作数类型: half

  步骤2: _9254 = tmp_0
    结果类型: half3
    操作数类型: half3

==================================================
第131行运算过程: half3 _9255 = (_9248.xyz * _9251) - _9254;
运算过程详情:
  步骤1: tmp_0 = _9248.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = tmp_0 * _9251
    结果类型: half
    操作数类型: half3, half

  步骤3: tmp_2 = tmp_1 - _9254
    结果类型: half
    操作数类型: half, half3

  步骤4: _9255 = tmp_2
    结果类型: half3
    操作数类型: half

==================================================
第132行运算过程: half _9257 = _9255.x;
运算过程详情:
  步骤1: tmp_0 = _9255.x
    结果类型: half
    操作数类型: half

  步骤2: _9257 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第133行运算过程: half _9263 = _9255.y;
运算过程详情:
  步骤1: tmp_0 = _9255.y
    结果类型: half
    操作数类型: half

  步骤2: _9263 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第134行运算过程: half _9270 = _9255.z;
运算过程详情:
  步骤1: tmp_0 = _9255.z
    结果类型: half
    操作数类型: half

  步骤2: _9270 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第135行运算过程: float3 _9279 = float3(((in.IN_WorldTangent.xyz * _9257) + (in.IN_WorldBinormal.xyz * _9263)) + (in.IN_WorldNormal.xyz * _9270));
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldTangent.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = tmp_0 * _9257
    结果类型: half
    操作数类型: half3, half

  步骤3: tmp_2 = in.IN_WorldBinormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤4: tmp_3 = tmp_2 * _9263
    结果类型: half
    操作数类型: half3, half

  步骤5: tmp_4 = tmp_1 + tmp_3
    结果类型: half
    操作数类型: half, half

  步骤6: tmp_5 = in.IN_WorldNormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤7: tmp_6 = tmp_5 * _9270
    结果类型: half
    操作数类型: half3, half

  步骤8: tmp_7 = tmp_4 + tmp_6
    结果类型: half
    操作数类型: half, half

  步骤9: tmp_8 = float3(tmp_7)
    结果类型: float3
    操作数类型: half

  步骤10: _9279 = tmp_8
    结果类型: float3
    操作数类型: float3

==================================================
第136行运算过程: float3 _9286 = float3(in.IN_StaticWorldNormal.xyz);
运算过程详情:
  步骤1: tmp_0 = in.IN_StaticWorldNormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: _9286 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第137行运算过程: float3 _9331 = (((float4(float3(in.IN_WorldTangent.xyz), 0.0) * _Block1.Local) * float(_9257)) + ((float4(float3(in.IN_WorldBinormal.xyz), 0.0) * _Block1.Local) * float(_9263))) + (_9286 * float(_9270));
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldTangent.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = float4(tmp_1, 0.0)
    结果类型: float4
    操作数类型: float3, float

  步骤4: tmp_3 = _Block1.Local
    结果类型: float3x4
    操作数类型: float3x4

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float4
    操作数类型: float4, float3x4

  步骤6: tmp_5 = float(_9257)
    结果类型: float
    操作数类型: half

  步骤7: tmp_6 = tmp_4 * tmp_5
    结果类型: float
    操作数类型: float4, float

  步骤8: tmp_7 = in.IN_WorldBinormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤9: tmp_8 = float3(tmp_7)
    结果类型: float3
    操作数类型: half3

  步骤10: tmp_9 = float4(tmp_8, 0.0)
    结果类型: float4
    操作数类型: float3, float

  步骤11: tmp_10 = _Block1.Local
    结果类型: float3x4
    操作数类型: float3x4

  步骤12: tmp_11 = tmp_9 * tmp_10
    结果类型: float4
    操作数类型: float4, float3x4

  步骤13: tmp_12 = float(_9263)
    结果类型: float
    操作数类型: half

  步骤14: tmp_13 = tmp_11 * tmp_12
    结果类型: float
    操作数类型: float4, float

  步骤15: tmp_14 = tmp_6 + tmp_13
    结果类型: float
    操作数类型: float, float

  步骤16: tmp_15 = float(_9270)
    结果类型: float
    操作数类型: half

  步骤17: tmp_16 = _9286 * tmp_15
    结果类型: float
    操作数类型: float3, float

  步骤18: tmp_17 = tmp_14 + tmp_16
    结果类型: float
    操作数类型: float, float

  步骤19: _9331 = tmp_17
    结果类型: float3
    操作数类型: float

==================================================
第138行运算过程: half _9334 = half((_9331 * rsqrt(fast::max(dot(_9331, _9331), 9.9999997473787516355514526367188e-06))).y);
运算过程详情:
  步骤1: tmp_0 = dot(_9331, _9331)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::max(tmp_0, 9.9999997473787516355514526367188)
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = rsqrt(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = _9331 * tmp_2
    结果类型: float
    操作数类型: float3, float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: _9334 = tmp_4
    结果类型: half
    操作数类型: half

==================================================
第139行运算过程: half3 _9064 = mix(in.IN_WorldNormal.xyz, half3(_9279 * rsqrt(fast::max(dot(_9279, _9279), 9.9999997473787516355514526367188e-06))), half3(half(_Block1.cNormalMapStrength)));
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldNormal.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = dot(_9279, _9279)
    结果类型: float
    操作数类型: float3, float3

  步骤3: tmp_2 = fast::max(tmp_1, 9.9999997473787516355514526367188)
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = rsqrt(tmp_2)
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _9279 * tmp_3
    结果类型: float
    操作数类型: float3, float

  步骤6: tmp_5 = half3(tmp_4)
    结果类型: half3
    操作数类型: float

  步骤7: tmp_6 = mix(tmp_0, tmp_5)
    结果类型: half3
    操作数类型: half3, half3

  步骤8: _9064 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第140行运算过程: half4 _9074 = sMixSampler.sample(sMixSamplerSmplr, in.IN_TexCoord.xy);
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = sMixSampler.sample(sMixSamplerSmplr, tmp_0)
    结果类型: float
    操作数类型: unknown, float2

  步骤3: _9074 = tmp_1
    结果类型: half4
    操作数类型: float

==================================================
第141行运算过程: half _9079 = _9074.y;
运算过程详情:
  步骤1: tmp_0 = _9074.y
    结果类型: half
    操作数类型: half

  步骤2: _9079 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第142行运算过程: half _9096 = half(mix(1.0, float(mix(half3(_9019), half3(half(fast::clamp(_9014 + _Block1.cAOoffset, 0.0, 1.0))), in.IN_TintColor.xxx).x * _9074.z), float(_8929)));
运算过程详情:
  步骤1: tmp_0 = half3(_9019)
    结果类型: half3
    操作数类型: half

  步骤2: tmp_1 = _Block1.cAOoffset
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _9014 + tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = half3(tmp_4)
    结果类型: half3
    操作数类型: half

  步骤7: tmp_6 = in.IN_TintColor.xxx
    结果类型: half3
    操作数类型: half3

  步骤8: tmp_7 = mix(tmp_0, tmp_5, tmp_6)
    结果类型: half3
    操作数类型: half3, half3, half3

  步骤9: tmp_8 = float(tmp_7)
    结果类型: float
    操作数类型: half3

  步骤10: tmp_9 = mix(1.0, tmp_8)
    结果类型: float
    操作数类型: float, float

  步骤11: tmp_10 = half(tmp_9)
    结果类型: half
    操作数类型: float

  步骤12: _9096 = tmp_10
    结果类型: half
    操作数类型: half

==================================================
第143行运算过程: float _9100 = float(_9096);
运算过程详情:
  步骤1: tmp_0 = float(_9096)
    结果类型: float
    操作数类型: half

  步骤2: _9100 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第144行运算过程: float3 _9109 = float3(_9064);
运算过程详情:
  步骤1: tmp_0 = float3(_9064)
    结果类型: float3
    操作数类型: half3

  步骤2: _9109 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第145行运算过程: half4 _9130 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, in.IN_TexCoord.xy);
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = sEmissionMapSampler.sample(sEmissionMapSamplerSmplr, tmp_0)
    结果类型: float
    操作数类型: unknown, float2

  步骤3: _9130 = tmp_1
    结果类型: half4
    操作数类型: float

==================================================
第146行运算过程: half3 _9145 = _9130.xyz * half3((float3(_Block1.cEmissionColor) * _Block1.cEmissionScale) * float3(_8949));
运算过程详情:
  步骤1: tmp_0 = _9130.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = _Block1.cEmissionColor
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: tmp_3 = _Block1.cEmissionScale
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float
    操作数类型: float3, float

  步骤6: tmp_5 = float3(_8949)
    结果类型: float3
    操作数类型: half3

  步骤7: tmp_6 = tmp_4 * tmp_5
    结果类型: float
    操作数类型: float, float3

  步骤8: tmp_7 = half3(tmp_6)
    结果类型: half3
    操作数类型: float

  步骤9: tmp_8 = tmp_0 * tmp_7
    结果类型: half3
    操作数类型: half3, half3

  步骤10: _9145 = tmp_8
    结果类型: half3
    操作数类型: half3

==================================================
第148行运算过程: if (!gl_FrontFacing)
  无运算过程
==================================================
第150行运算过程: _18217 = _9334 * half(-1);
运算过程详情:
  步骤1: tmp_0 = -1
    结果类型: int
    操作数类型: int

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: int

  步骤3: tmp_2 = _9334 * tmp_1
    结果类型: half
    操作数类型: half, half

  步骤4: _18217 = tmp_2
    操作数类型: half

==================================================
第154行运算过程: _18217 = _9334;
运算过程详情:
  步骤1: _18217 = _9334
    操作数类型: half

==================================================
第156行运算过程: float3 _9698 = float3(_9179);
运算过程详情:
  步骤1: tmp_0 = float3(_9179)
    结果类型: float3
    操作数类型: half3

  步骤2: _9698 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第157行运算过程: float _9408 = mix(_Block1.cCIFadeTime.y, _Block1.cCIFadeTime.w, fast::clamp((_Block1.CameraPos.w - _Block1.cCIFadeTime.x) * _Block1.cCIFadeTime.z, 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = _Block1.cCIFadeTime.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.cCIFadeTime.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _Block1.CameraPos.w
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = _Block1.cCIFadeTime.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = tmp_2 - tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _Block1.cCIFadeTime.z
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = tmp_4 * tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = fast::clamp(tmp_6, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤9: tmp_8 = mix(tmp_0, tmp_1, tmp_7)
    结果类型: float
    操作数类型: float, float, float

  步骤10: _9408 = tmp_8
    结果类型: float
    操作数类型: float

==================================================
第158行运算过程: float _9721 = fast::clamp(dot(_9286, float3(half3(_9176, _9176, _9199))), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = half3(_9176, _9176, _9199)
    结果类型: half3
    操作数类型: half, half, half

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = dot(_9286, tmp_1)
    结果类型: float
    操作数类型: float3, float3

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _9721 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第159行运算过程: float _9734 = (_9721 * _9721) * float(half(fast::clamp((_Block1.cCIMudBuff[2u] - 1.0) * 0.699999988079071044921875, 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = _9721 * _9721
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = _Block1.cCIMudBuff
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = fast::clamp(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float3

  步骤5: tmp_4 = float(tmp_3)
    结果类型: float
    操作数类型: half

  步骤6: tmp_5 = tmp_0 * tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _9734 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第160行运算过程: half3 _9761 = mix(_8949, half3(half(0.20700000226497650146484375), half(0.18400000035762786865234375), half(0.1369999945163726806640625)), half3(half(_9734 * _9408)));
运算过程详情:
  步骤1: tmp_0 = half(0.20700000226497650146484375)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half(0.18400000035762786865234375)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half(0.1369999945163726806640625)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_0, tmp_1, tmp_2)
    结果类型: half3
    操作数类型: half, half, half

  步骤5: tmp_4 = _9734 * _9408
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = half(tmp_4)
    结果类型: half
    操作数类型: float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: half

  步骤8: tmp_7 = mix(_8949, tmp_3, tmp_6)
    结果类型: half3
    操作数类型: half3, half3, half3

  步骤9: _9761 = tmp_7
    结果类型: half3
    操作数类型: half3

==================================================
第161行运算过程: half _9772 = half(mix(float(_9199 - _9074.x), 0.89999997615814208984375, _9734 * float(half(mix(_9408 - (0.5 * _Block1.cCIFadeTime.x), _9408, _Block1.cCIFadeTime.y)))));
运算过程详情:
  步骤1: tmp_0 = _9074.x
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = _9199 - tmp_0
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: tmp_3 = _Block1.cCIFadeTime.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = 0.5 * tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _9408 - tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = _Block1.cCIFadeTime.y
    结果类型: float
    操作数类型: float

  步骤8: tmp_7 = mix(tmp_5, _9408, tmp_6)
    结果类型: float
    操作数类型: float, float, float

  步骤9: tmp_8 = half(tmp_7)
    结果类型: half
    操作数类型: float

  步骤10: tmp_9 = float(tmp_8)
    结果类型: float
    操作数类型: half

  步骤11: tmp_10 = _9734 * tmp_9
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = mix(tmp_2, 0.89999997615814208984375, tmp_10)
    结果类型: float
    操作数类型: float, float, float

  步骤13: tmp_12 = half(tmp_11)
    结果类型: half
    操作数类型: float

  步骤14: _9772 = tmp_12
    结果类型: half
    操作数类型: half

==================================================
第162行运算过程: float _9429 = (float(half(_9014)) * _9408) * fast::clamp(1.0 + _Block1.EnvInfo.y, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = half(_9014)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: tmp_2 = tmp_1 * _9408
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = _Block1.EnvInfo.y
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = 1.0 + tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::clamp(tmp_4, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤7: tmp_6 = tmp_2 * tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: _9429 = tmp_6
    结果类型: float
    操作数类型: float

==================================================
第163行运算过程: float4 _9443 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, ((in.IN_TexCoord.xy * _Block1.cCISnowData.x) * 12.0));
运算过程详情:
  步骤1: tmp_0 = in.IN_TexCoord.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _Block1.cCISnowData.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float2, float

  步骤4: tmp_3 = tmp_2 * 12.0
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = sCharInteractionSampler.sample(sCharInteractionSamplerSmplr, tmp_3)
    结果类型: float
    操作数类型: unknown, float

  步骤6: _9443 = tmp_4
    结果类型: float4
    操作数类型: float

==================================================
第169行运算过程: if (_Block1.cCISwitchData.x > 0.0)
  无运算过程
==================================================
第171行运算过程: float _9460 = float(half(fast::max(0.0, _Block1.EnvInfo.y)));
运算过程详情:
  步骤1: tmp_0 = _Block1.EnvInfo.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::max(0.0, tmp_0)
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: _9460 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第172行运算过程: float _9462 = fast::min(2.0, fast::clamp(_Block1.cCISnowData.z, 0.0, 1.0) + _9460);
运算过程详情:
  步骤1: tmp_0 = _Block1.cCISnowData.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = tmp_1 + _9460
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::min(2.0, tmp_2)
    结果类型: float
    操作数类型: float, float

  步骤5: _9462 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第173行运算过程: float _9499 = 1.0 - _9429;
运算过程详情:
  步骤1: tmp_0 = 1.0 - _9429
    结果类型: float
    操作数类型: float, float

  步骤2: _9499 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第174行运算过程: float _9505 = _9443.y;
运算过程详情:
  步骤1: tmp_0 = _9443.y
    结果类型: float
    操作数类型: float

  步骤2: _9505 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第175行运算过程: float _9510 = float(half(fast::clamp(((float2(0.800000011920928955078125, 0.5) * _9429).x * (1.0 - powr(float(half(powr(float(clamp(_18217, half(0.0), half(1.0))) + fast::clamp(0.20000000298023223876953125 - fast::clamp((-1.0) * _Block1.cCISnowData.z, 0.0, 1.0), 0.0, 1.0), 2.0 - _9462) * float(in.IN_StaticWorldNormal.w))), 0.800000011920928955078125 - (_9462 * 0.4000000059604644775390625)))) + ((_9499 * _9499) * _9499), 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = float2(0.800000011920928955078125, 0.5)
    结果类型: float2
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * _9429
    结果类型: float
    操作数类型: float2, float

  步骤3: tmp_2 = fast::clamp(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = float(tmp_3)
    结果类型: float
    操作数类型: half

  步骤6: _9510 = tmp_4
    结果类型: float
    操作数类型: float

==================================================
第176行运算过程: float _9519 = float(half(9.9956989288330078125e-05));
运算过程详情:
  步骤1: tmp_0 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: _9519 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第177行运算过程: half _9535 = half(1.0 - fast::clamp((float(in.IN_LocalPosition.y) * (2.0 - _9460)) * _Block1.cCISnowData.y, 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = in.IN_LocalPosition.y
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: tmp_2 = 2.0 - _9460
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = _Block1.cCISnowData.y
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = tmp_3 * tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤8: tmp_7 = 1.0 - tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = half(tmp_7)
    结果类型: half
    操作数类型: float

  步骤10: _9535 = tmp_8
    结果类型: half
    操作数类型: half

==================================================
第178行运算过程: float _9556 = float(half((float(_9535) * fast::clamp(float(_9535 + _18217) + 0.5, 0.0, 1.0)) * _9429));
运算过程详情:
  步骤1: tmp_0 = float(_9535)
    结果类型: float
    操作数类型: half

  步骤2: tmp_1 = _9535 + _18217
    结果类型: half
    操作数类型: half, unknown

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: tmp_3 = tmp_2 + 0.5
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::clamp(tmp_3, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤6: tmp_5 = tmp_0 * tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = tmp_5 * _9429
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = half(tmp_6)
    结果类型: half
    操作数类型: float

  步骤9: tmp_8 = float(tmp_7)
    结果类型: float
    操作数类型: half

  步骤10: _9556 = tmp_8
    结果类型: float
    操作数类型: float

==================================================
第179行运算过程: float _9557 = 1.0 - _9556;
运算过程详情:
  步骤1: tmp_0 = 1.0 - _9556
    结果类型: float
    操作数类型: float, float

  步骤2: _9557 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第180行运算过程: half _9585 = half((_Block1.cCISwitchData.x * fast::clamp(1.0 - _Block1.cCISnowData.w, 0.0, 1.0)) * float(max(half(fast::clamp((fast::max(_9505, _9443.w) - _9510) / fast::max(_9519, fast::clamp(_9510 + 0.1500000059604644775390625, 0.0, 1.0) - _9510), 0.0, 1.0)), half(fast::clamp((fast::max(_9505, _9443.z) - _9557) / fast::max(_9519, (1.5 - _9556) - _9557), 0.0, 1.0)))));
运算过程详情:
  步骤1: tmp_0 = _Block1.cCISwitchData.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.cCISnowData.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = 1.0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: tmp_4 = tmp_0 * tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _9443.w
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = fast::max(_9505, tmp_5)
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_6 - _9510
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = _9510 + 0.1500000059604644775390625
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = fast::clamp(tmp_8, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤11: tmp_10 = tmp_9 - _9510
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = fast::max(_9519, tmp_10)
    结果类型: float
    操作数类型: float, float

  步骤13: tmp_12 = tmp_7 / tmp_11
    结果类型: float
    操作数类型: float, float

  步骤14: tmp_13 = fast::clamp(tmp_12, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤15: tmp_14 = half(tmp_13)
    结果类型: half
    操作数类型: float

  步骤16: tmp_15 = _9443.z
    结果类型: float
    操作数类型: float

  步骤17: tmp_16 = fast::max(_9505, tmp_15)
    结果类型: float
    操作数类型: float, float

  步骤18: tmp_17 = tmp_16 - _9557
    结果类型: float
    操作数类型: float, float

  步骤19: tmp_18 = 1.5 - _9556
    结果类型: float
    操作数类型: float, float

  步骤20: tmp_19 = tmp_18 - _9557
    结果类型: float
    操作数类型: float, float

  步骤21: tmp_20 = fast::max(_9519, tmp_19)
    结果类型: float
    操作数类型: float, float

  步骤22: tmp_21 = tmp_17 / tmp_20
    结果类型: float
    操作数类型: float, float

  步骤23: tmp_22 = fast::clamp(tmp_21, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤24: tmp_23 = half(tmp_22)
    结果类型: half
    操作数类型: float

  步骤25: tmp_24 = max(tmp_14, tmp_23)
    结果类型: half
    操作数类型: half, half

  步骤26: tmp_25 = float(tmp_24)
    结果类型: float
    操作数类型: half

  步骤27: tmp_26 = tmp_4 * tmp_25
    结果类型: float
    操作数类型: float, float

  步骤28: tmp_27 = half(tmp_26)
    结果类型: half
    操作数类型: float

  步骤29: _9585 = tmp_27
    结果类型: half
    操作数类型: half

==================================================
第181行运算过程: half _9588 = _9199 - _9585;
运算过程详情:
  步骤1: tmp_0 = _9199 - _9585
    结果类型: half
    操作数类型: half, half

  步骤2: _9588 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第182行运算过程: float _9603 = float(_9585);
运算过程详情:
  步骤1: tmp_0 = float(_9585)
    结果类型: float
    操作数类型: half

  步骤2: _9603 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第183行运算过程: _18272 = half(mix(float(_9772), 1.0, _9603));
运算过程详情:
  步骤1: tmp_0 = float(_9772)
    结果类型: float
    操作数类型: half

  步骤2: tmp_1 = mix(tmp_0, 1.0, _9603)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: _18272 = tmp_2
    操作数类型: half

==================================================
第184行运算过程: _18268 = _9145 * _9588;
运算过程详情:
  步骤1: tmp_0 = _9145 * _9588
    结果类型: half
    操作数类型: half3, half

  步骤2: _18268 = tmp_0
    操作数类型: half

==================================================
第185行运算过程: _18236 = half(mix(_9100, 1.0, _9603));
运算过程详情:
  步骤1: tmp_0 = mix(_9100, 1.0, _9603)
    结果类型: float
    操作数类型: float, float, float

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: _18236 = tmp_1
    操作数类型: half

==================================================
第186行运算过程: _18234 = _9079 * _9588;
运算过程详情:
  步骤1: tmp_0 = _9079 * _9588
    结果类型: half
    操作数类型: half, half

  步骤2: _18234 = tmp_0
    操作数类型: half

==================================================
第187行运算过程: _18225 = mix(_9761, half3(half(0.61000001430511474609375), half(0.660000026226043701171875), half(0.790000021457672119140625)), half3(_9585));
运算过程详情:
  步骤1: tmp_0 = half(0.61000001430511474609375)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half(0.660000026226043701171875)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half(0.790000021457672119140625)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_0, tmp_1, tmp_2)
    结果类型: half3
    操作数类型: half, half, half

  步骤5: tmp_4 = half3(_9585)
    结果类型: half3
    操作数类型: half

  步骤6: tmp_5 = mix(_9761, tmp_3, tmp_4)
    结果类型: half3
    操作数类型: half3, half3, half3

  步骤7: _18225 = tmp_5
    操作数类型: half3

==================================================
第191行运算过程: _18272 = _9772;
运算过程详情:
  步骤1: _18272 = _9772
    操作数类型: half

==================================================
第192行运算过程: _18268 = _9145;
运算过程详情:
  步骤1: _18268 = _9145
    操作数类型: half3

==================================================
第193行运算过程: _18236 = _9096;
运算过程详情:
  步骤1: _18236 = _9096
    操作数类型: half

==================================================
第194行运算过程: _18234 = _9079;
运算过程详情:
  步骤1: _18234 = _9079
    操作数类型: half

==================================================
第195行运算过程: _18225 = _9761;
运算过程详情:
  步骤1: _18225 = _9761
    操作数类型: half3

==================================================
第197行运算过程: half _8295 = half(fast::clamp(1.0 - _Block1.HexRenderOptionData[0].x, 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = _Block1.HexRenderOptionData
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float4

  步骤3: tmp_2 = fast::clamp(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: _8295 = tmp_3
    结果类型: half
    操作数类型: half

==================================================
第198行运算过程: float _8298 = float(_8939.w * half((_8991 * float(min(_8994, _8996))) * powr(float(max(in.IN_TintColor.x, half(9.9956989288330078125e-05))), _Block1.cFurFadeInt)));
运算过程详情:
  步骤1: tmp_0 = _8939.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = min(_8994, _8996)
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: tmp_3 = _8991 * tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = in.IN_TintColor.x
    结果类型: half
    操作数类型: half

  步骤6: tmp_5 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤7: tmp_6 = max(tmp_4, tmp_5)
    结果类型: half
    操作数类型: half, half

  步骤8: tmp_7 = float(tmp_6)
    结果类型: float
    操作数类型: half

  步骤9: tmp_8 = powr(tmp_7)
    结果类型: float
    操作数类型: float

  步骤10: tmp_9 = tmp_3 * tmp_8
    结果类型: float
    操作数类型: float, float

  步骤11: tmp_10 = half(tmp_9)
    结果类型: half
    操作数类型: float

  步骤12: tmp_11 = tmp_0 * tmp_10
    结果类型: half
    操作数类型: half, half

  步骤13: tmp_12 = float(tmp_11)
    结果类型: float
    操作数类型: half

  步骤14: _8298 = tmp_12
    结果类型: float
    操作数类型: float

==================================================
第199行运算过程: half3 _8303 = half3(half(0.2125999927520751953125), half(0.715200006961822509765625), half(0.072200000286102294921875));
运算过程详情:
  步骤1: tmp_0 = half(0.2125999927520751953125)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half(0.715200006961822509765625)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half(0.072200000286102294921875)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_0, tmp_1, tmp_2)
    结果类型: half3
    操作数类型: half, half, half

  步骤5: _8303 = tmp_3
    结果类型: half3
    操作数类型: half3

==================================================
第200行运算过程: half3 _8315 = mix(half3(dot(_18225, _8303)), _18225, half3(half(_Block1.cSaturation)));
运算过程详情:
  步骤1: tmp_0 = dot(_18225, _8303)
    结果类型: float
    操作数类型: unknown, half3

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float

  步骤3: tmp_2 = _Block1.cSaturation
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = half3(tmp_3)
    结果类型: half3
    操作数类型: half

  步骤6: tmp_5 = mix(tmp_1, _18225, tmp_4)
    结果类型: half3
    操作数类型: half3, unknown, half3

  步骤7: _8315 = tmp_5
    结果类型: half3
    操作数类型: half3

==================================================
第201行运算过程: half _9787 = half(_8298);
运算过程详情:
  步骤1: tmp_0 = half(_8298)
    结果类型: half
    操作数类型: float

  步骤2: _9787 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第204行运算过程: if (_Block1.eDynamicFresnelIntensity > 0.0)
  无运算过程
==================================================
第206行运算过程: float _9806 = abs(dot(_9109, -_8921));
运算过程详情:
  步骤1: tmp_0 = -_8921
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = dot(_9109, tmp_0)
    结果类型: float
    操作数类型: float3, float3

  步骤3: tmp_2 = abs(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: _9806 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第207行运算过程: float _9813 = abs(_Block1.eFresnelPower);
运算过程详情:
  步骤1: tmp_0 = _Block1.eFresnelPower
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: _9813 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第208行运算过程: float _9831 = fast::max(_Block1.eFresnelMinIntensity, (_Block1.eFresnelPower < 0.0) ? powr(_9806, _9813) : powr(1.0 - fast::min(float(_9199 - half(9.9956989288330078125e-05)), _9806), _9813));
运算过程详情:
  步骤1: tmp_0 = _Block1.eFresnelMinIntensity
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.eFresnelPower
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = fast::max(tmp_0, tmp_1)
    结果类型: float
    操作数类型: float, float

  步骤4: _9831 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第209行运算过程: float _9846 = float(_9787 * half(_9831));
运算过程详情:
  步骤1: tmp_0 = half(_9831)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = _9787 * tmp_0
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: _9846 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第210行运算过程: _18255 = _9179 + half3((((float3(_Block1.eFresnelColor) * _9831) * _Block1.eFresnelIntensity) * 1.0) * _9846);
运算过程详情:
  步骤1: tmp_0 = _Block1.eFresnelColor
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_1 * _9831
    结果类型: float
    操作数类型: float3, float

  步骤4: tmp_3 = _Block1.eFresnelIntensity
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_4 * 1.0
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = tmp_5 * _9846
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = half3(tmp_6)
    结果类型: half3
    操作数类型: float

  步骤9: tmp_8 = _9179 + tmp_7
    结果类型: half3
    操作数类型: half3, half3

  步骤10: _18255 = tmp_8
    操作数类型: half3

==================================================
第211行运算过程: _18230 = clamp(half(fast::max(fast::clamp(_Block1.eFresnelAlphaAdd, 0.0, 1.0) * _8298, _9846)), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = _Block1.eFresnelAlphaAdd
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = tmp_1 * _8298
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::max(tmp_2, _9846)
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤7: tmp_6 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤8: tmp_7 = clamp(tmp_4, tmp_5, tmp_6)
    结果类型: half
    操作数类型: half, half, half

  步骤9: _18230 = tmp_7
    操作数类型: half

==================================================
第215行运算过程: _18255 = _9179;
运算过程详情:
  步骤1: _18255 = _9179
    操作数类型: half3

==================================================
第216行运算过程: _18230 = _9787;
运算过程详情:
  步骤1: _18230 = _9787
    操作数类型: half

==================================================
第218行运算过程: float _9868 = float(_18230);
运算过程详情:
  步骤1: tmp_0 = float(_18230)
    结果类型: float
    操作数类型: unknown

  步骤2: _9868 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第219行运算过程: half _8346 = _18236 * half(_Block1.SHAOParam.w);
运算过程详情:
  步骤1: tmp_0 = _Block1.SHAOParam.w
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = _18236 * tmp_1
    结果类型: half
    操作数类型: unknown, half

  步骤4: _8346 = tmp_2
    结果类型: half
    操作数类型: half

==================================================
第220行运算过程: float4 _9926 = float4((in.IN_WorldPosition.xyz + (_8925 * _Block1.cBiasFarAwayShadow)) - _Block1.CameraPos.xyz, 1.0) * _Block1.ShadowViewProjTexs0;
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.cBiasFarAwayShadow
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _8925 * tmp_1
    结果类型: float
    操作数类型: float3, float

  步骤4: tmp_3 = tmp_0 + tmp_2
    结果类型: float
    操作数类型: float3, float

  步骤5: tmp_4 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤6: tmp_5 = tmp_3 - tmp_4
    结果类型: float
    操作数类型: float, float3

  步骤7: tmp_6 = float4(tmp_5, 1.0)
    结果类型: float4
    操作数类型: float, float

  步骤8: tmp_7 = _Block1.ShadowViewProjTexs0
    结果类型: float4x4
    操作数类型: float4x4

  步骤9: tmp_8 = tmp_6 * tmp_7
    结果类型: float4
    操作数类型: float4, float4x4

  步骤10: _9926 = tmp_8
    结果类型: float4
    操作数类型: float4

==================================================
第221行运算过程: float4 _17899 = _9926;
运算过程详情:
  步骤1: _17899 = _9926
    结果类型: float4
    操作数类型: float4

==================================================
第222行运算过程: _17899.z = _9926.z - _Block1.CSMShadowBiases.x;
运算过程详情:
  步骤1: tmp_0 = _9926.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _17899.z = tmp_2
    操作数类型: float

==================================================
第223行运算过程: float4 _9942 = float4(in.IN_WorldPosition.xyz, 1.0);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = float4(tmp_0, 1.0)
    结果类型: float4
    操作数类型: float3, float

  步骤3: _9942 = tmp_1
    结果类型: float4
    操作数类型: float4

==================================================
第224行运算过程: float4 _9945 = _9942 * _Block1.ShadowViewProjTexs1;
运算过程详情:
  步骤1: tmp_0 = _Block1.ShadowViewProjTexs1
    结果类型: float4x4
    操作数类型: float4x4

  步骤2: tmp_1 = _9942 * tmp_0
    结果类型: float4
    操作数类型: float4, float4x4

  步骤3: _9945 = tmp_1
    结果类型: float4
    操作数类型: float4

==================================================
第225行运算过程: float4 _17902 = _9945;
运算过程详情:
  步骤1: _17902 = _9945
    结果类型: float4
    操作数类型: float4

==================================================
第226行运算过程: _17902.z = _9945.z - _Block1.CSMShadowBiases.y;
运算过程详情:
  步骤1: tmp_0 = _9945.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _17902.z = tmp_2
    操作数类型: float

==================================================
第228行运算过程: if (_Block1.CSMCacheIndexs.z > 0.0)
  无运算过程
==================================================
第230行运算过程: float4 _9971 = _9942 * _Block1.ShadowViewProjTexs2;
运算过程详情:
  步骤1: tmp_0 = _Block1.ShadowViewProjTexs2
    结果类型: float4x4
    操作数类型: float4x4

  步骤2: tmp_1 = _9942 * tmp_0
    结果类型: float4
    操作数类型: float4, float4x4

  步骤3: _9971 = tmp_1
    结果类型: float4
    操作数类型: float4

==================================================
第231行运算过程: _9971.z = _9971.z - _Block1.CSMShadowBiases.z;
运算过程详情:
  步骤1: tmp_0 = _9971.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _9971.z = tmp_2
    操作数类型: float

==================================================
第232行运算过程: _18237 = _9971;
运算过程详情:
  步骤1: _18237 = _9971
    操作数类型: float4

==================================================
第236行运算过程: _18237 = float4(0.0, 0.0, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = float4(0.0, 0.0, 0.0, 1.0)
    结果类型: float4
    操作数类型: float, float, float, float

  步骤2: _18237 = tmp_0
    操作数类型: float4

==================================================
第238行运算过程: float3 _10033 = _17902.xyz / float3(_9945.w);
运算过程详情:
  步骤1: tmp_0 = _17902.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _9945.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: _10033 = tmp_3
    结果类型: float3
    操作数类型: float3

==================================================
第239行运算过程: float3 _10040 = _18237.xyz / float3(_18237.w);
运算过程详情:
  步骤1: tmp_0 = _18237.xyz
    结果类型: unknown
    操作数类型: unknown

  步骤2: tmp_1 = _18237.w
    结果类型: unknown
    操作数类型: unknown

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: unknown

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: unknown, float3

  步骤5: _10040 = tmp_3
    结果类型: float3
    操作数类型: float3

==================================================
第240行运算过程: float3 _10077 = _10040 * (step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.z) * float(all(_10040 > float3(0.0)) && all(_10040 < float3(1.0))));
运算过程详情:
  步骤1: tmp_0 = -0.100000001490116119384765625
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = step(tmp_0, tmp_1)
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = all(_10040)
    结果类型: float
    操作数类型: float3

  步骤5: tmp_4 = float(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = tmp_2 * tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = _10040 * tmp_5
    结果类型: float
    操作数类型: float3, float

  步骤8: _10077 = tmp_6
    结果类型: float3
    操作数类型: float

==================================================
第241行运算过程: float _21135 = step(-0.100000001490116119384765625, _Block1.CSMShadowBiases.y) * float(all(_10033 > float3(0.0)) && all(_10033 < float3(1.0)));
运算过程详情:
  步骤1: tmp_0 = -0.100000001490116119384765625
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.CSMShadowBiases.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = step(tmp_0, tmp_1)
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = all(_10033)
    结果类型: float
    操作数类型: float3

  步骤5: tmp_4 = float(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = tmp_2 * tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _21135 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第242行运算过程: float3 _21138 = _10077 + ((_10033 - _10077) * _21135);
运算过程详情:
  步骤1: tmp_0 = _10033 - _10077
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = tmp_0 * _21135
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = _10077 + tmp_1
    结果类型: float
    操作数类型: float3, float

  步骤4: _21138 = tmp_2
    结果类型: float3
    操作数类型: float

==================================================
第243行运算过程: float _10113 = _21138.z;
运算过程详情:
  步骤1: tmp_0 = _21138.z
    结果类型: float
    操作数类型: float

  步骤2: _10113 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第244行运算过程: float2 _10120 = float2(_Block1.cShadowBias.w);
运算过程详情:
  步骤1: tmp_0 = _Block1.cShadowBias.w
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _10120 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第245行运算过程: float2 _10167 = (_21138.xy / _10120) - float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _21138.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = tmp_0 / _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤3: tmp_2 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤4: tmp_3 = tmp_1 - tmp_2
    结果类型: float2
    操作数类型: float2, float2

  步骤5: _10167 = tmp_3
    结果类型: float2
    操作数类型: float2

==================================================
第246行运算过程: float2 _10169 = fract(_10167);
运算过程详情:
  步骤1: tmp_0 = fract(_10167)
    结果类型: float2
    操作数类型: float2

  步骤2: _10169 = tmp_0
    结果类型: float2
    操作数类型: float2

==================================================
第247行运算过程: float2 _10171 = floor(_10167);
运算过程详情:
  步骤1: tmp_0 = floor(_10167)
    结果类型: float2
    操作数类型: float2

  步骤2: _10171 = tmp_0
    结果类型: float2
    操作数类型: float2

==================================================
第248行运算过程: float2 _10177 = float2(2.0) - _10169;
运算过程详情:
  步骤1: tmp_0 = float2(2.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = tmp_0 - _10169
    结果类型: float2
    操作数类型: float2, float2

  步骤3: _10177 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第249行运算过程: float2 _10181 = _10169 + float2(1.0);
运算过程详情:
  步骤1: tmp_0 = float2(1.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = _10169 + tmp_0
    结果类型: float2
    操作数类型: float2, float2

  步骤3: _10181 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第250行运算过程: float2 _10184 = float2(1.0) / _10177;
运算过程详情:
  步骤1: tmp_0 = float2(1.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = tmp_0 / _10177
    结果类型: float2
    操作数类型: float2, float2

  步骤3: _10184 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第251行运算过程: float2 _10187 = _10169 / _10181;
运算过程详情:
  步骤1: tmp_0 = _10169 / _10181
    结果类型: float2
    操作数类型: float2, float2

  步骤2: _10187 = tmp_0
    结果类型: float2
    操作数类型: float2

==================================================
第252行运算过程: float _10205 = float(int(_Block1.CSMCacheIndexs[int(2.0 + ((-1.0) * _21135))]));
运算过程详情:
  步骤1: tmp_0 = _Block1.CSMCacheIndexs
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: float4

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: int

  步骤4: _10205 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第253行运算过程: float3 _10208 = float3(((_10171 + float2(-0.5)) + _10184) * _10120, _10205);
运算过程详情:
  步骤1: tmp_0 = -0.5
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = _10171 + tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 + _10184
    结果类型: float2
    操作数类型: float2, float2

  步骤5: tmp_4 = tmp_3 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤6: tmp_5 = float3(tmp_4, _10205)
    结果类型: float3
    操作数类型: float2, float

  步骤7: _10208 = tmp_5
    结果类型: float3
    操作数类型: float3

==================================================
第254行运算过程: float3 _10231 = float3(((_10171 + float2(1.5, -0.5)) + float2(_10187.x, _10184.y)) * _10120, _10205);
运算过程详情:
  步骤1: tmp_0 = -0.5
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(1.5, tmp_0)
    结果类型: float2
    操作数类型: float, float

  步骤3: tmp_2 = _10171 + tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = _10187.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _10184.y
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = float2(tmp_3, tmp_4)
    结果类型: float2
    操作数类型: float, float

  步骤7: tmp_6 = tmp_2 + tmp_5
    结果类型: float2
    操作数类型: float2, float2

  步骤8: tmp_7 = tmp_6 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤9: tmp_8 = float3(tmp_7, _10205)
    结果类型: float3
    操作数类型: float2, float

  步骤10: _10231 = tmp_8
    结果类型: float3
    操作数类型: float3

==================================================
第255行运算过程: float3 _10254 = float3(((_10171 + float2(-0.5, 1.5)) + float2(_10184.x, _10187.y)) * _10120, _10205);
运算过程详情:
  步骤1: tmp_0 = -0.5
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(tmp_0, 1.5)
    结果类型: float2
    操作数类型: float, float

  步骤3: tmp_2 = _10171 + tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = _10184.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _10187.y
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = float2(tmp_3, tmp_4)
    结果类型: float2
    操作数类型: float, float

  步骤7: tmp_6 = tmp_2 + tmp_5
    结果类型: float2
    操作数类型: float2, float2

  步骤8: tmp_7 = tmp_6 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤9: tmp_8 = float3(tmp_7, _10205)
    结果类型: float3
    操作数类型: float2, float

  步骤10: _10254 = tmp_8
    结果类型: float3
    操作数类型: float3

==================================================
第256行运算过程: float3 _10276 = float3(((_10171 + float2(1.5)) + _10187) * _10120, _10205);
运算过程详情:
  步骤1: tmp_0 = float2(1.5)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = _10171 + tmp_0
    结果类型: float2
    操作数类型: float2, float2

  步骤3: tmp_2 = tmp_1 + _10187
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤5: tmp_4 = float3(tmp_3, _10205)
    结果类型: float3
    操作数类型: float2, float

  步骤6: _10276 = tmp_4
    结果类型: float3
    操作数类型: float3

==================================================
第257行运算过程: float _10282 = _10177.x;
运算过程详情:
  步骤1: tmp_0 = _10177.x
    结果类型: float
    操作数类型: float

  步骤2: _10282 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第258行运算过程: float _10289 = _10181.x;
运算过程详情:
  步骤1: tmp_0 = _10181.x
    结果类型: float
    操作数类型: float

  步骤2: _10289 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第259行运算过程: float _10300 = _10181.y;
运算过程详情:
  步骤1: tmp_0 = _10181.y
    结果类型: float
    操作数类型: float

  步骤2: _10300 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第260行运算过程: float3 _9997 = _17899.xyz / float3(_9926.w);
运算过程详情:
  步骤1: tmp_0 = _17899.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _9926.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: _9997 = tmp_3
    结果类型: float3
    操作数类型: float3

==================================================
第261行运算过程: float _10004 = fast::min(1.0 - float(half(10) * half(9.9956989288330078125e-05)), _9997.z);
运算过程详情:
  步骤1: tmp_0 = half(10)
    结果类型: half
    操作数类型: int

  步骤2: tmp_1 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: tmp_4 = 1.0 - tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::min(tmp_4)
    结果类型: float
    操作数类型: float

  步骤7: _10004 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第262行运算过程: float3 _17928 = _9997;
运算过程详情:
  步骤1: _17928 = _9997
    结果类型: float3
    操作数类型: float3

==================================================
第263行运算过程: _17928.z = _10004;
运算过程详情:
  步骤1: _17928.z = _10004
    操作数类型: float

==================================================
第264行运算过程: float2 _10378 = (_17928.xy / _10120) - float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _17928.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = tmp_0 / _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤3: tmp_2 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤4: tmp_3 = tmp_1 - tmp_2
    结果类型: float2
    操作数类型: float2, float2

  步骤5: _10378 = tmp_3
    结果类型: float2
    操作数类型: float2

==================================================
第265行运算过程: float2 _10380 = fract(_10378);
运算过程详情:
  步骤1: tmp_0 = fract(_10378)
    结果类型: float2
    操作数类型: float2

  步骤2: _10380 = tmp_0
    结果类型: float2
    操作数类型: float2

==================================================
第266行运算过程: float2 _10382 = floor(_10378);
运算过程详情:
  步骤1: tmp_0 = floor(_10378)
    结果类型: float2
    操作数类型: float2

  步骤2: _10382 = tmp_0
    结果类型: float2
    操作数类型: float2

==================================================
第267行运算过程: float2 _10388 = float2(2.0) - _10380;
运算过程详情:
  步骤1: tmp_0 = float2(2.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = tmp_0 - _10380
    结果类型: float2
    操作数类型: float2, float2

  步骤3: _10388 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第268行运算过程: float2 _10392 = _10380 + float2(1.0);
运算过程详情:
  步骤1: tmp_0 = float2(1.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = _10380 + tmp_0
    结果类型: float2
    操作数类型: float2, float2

  步骤3: _10392 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第269行运算过程: float2 _10395 = float2(1.0) / _10388;
运算过程详情:
  步骤1: tmp_0 = float2(1.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = tmp_0 / _10388
    结果类型: float2
    操作数类型: float2, float2

  步骤3: _10395 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第270行运算过程: float2 _10398 = _10380 / _10392;
运算过程详情:
  步骤1: tmp_0 = _10380 / _10392
    结果类型: float2
    操作数类型: float2, float2

  步骤2: _10398 = tmp_0
    结果类型: float2
    操作数类型: float2

==================================================
第271行运算过程: float _10416 = float(int(_Block1.CSMCacheIndexs.x));
运算过程详情:
  步骤1: tmp_0 = _Block1.CSMCacheIndexs.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: float

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: int

  步骤4: _10416 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第272行运算过程: float3 _10419 = float3(((_10382 + float2(-0.5)) + _10395) * _10120, _10416);
运算过程详情:
  步骤1: tmp_0 = -0.5
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = _10382 + tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 + _10395
    结果类型: float2
    操作数类型: float2, float2

  步骤5: tmp_4 = tmp_3 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤6: tmp_5 = float3(tmp_4, _10416)
    结果类型: float3
    操作数类型: float2, float

  步骤7: _10419 = tmp_5
    结果类型: float3
    操作数类型: float3

==================================================
第273行运算过程: float3 _10442 = float3(((_10382 + float2(1.5, -0.5)) + float2(_10398.x, _10395.y)) * _10120, _10416);
运算过程详情:
  步骤1: tmp_0 = -0.5
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(1.5, tmp_0)
    结果类型: float2
    操作数类型: float, float

  步骤3: tmp_2 = _10382 + tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = _10398.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _10395.y
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = float2(tmp_3, tmp_4)
    结果类型: float2
    操作数类型: float, float

  步骤7: tmp_6 = tmp_2 + tmp_5
    结果类型: float2
    操作数类型: float2, float2

  步骤8: tmp_7 = tmp_6 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤9: tmp_8 = float3(tmp_7, _10416)
    结果类型: float3
    操作数类型: float2, float

  步骤10: _10442 = tmp_8
    结果类型: float3
    操作数类型: float3

==================================================
第274行运算过程: float3 _10465 = float3(((_10382 + float2(-0.5, 1.5)) + float2(_10395.x, _10398.y)) * _10120, _10416);
运算过程详情:
  步骤1: tmp_0 = -0.5
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float2(tmp_0, 1.5)
    结果类型: float2
    操作数类型: float, float

  步骤3: tmp_2 = _10382 + tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = _10395.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _10398.y
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = float2(tmp_3, tmp_4)
    结果类型: float2
    操作数类型: float, float

  步骤7: tmp_6 = tmp_2 + tmp_5
    结果类型: float2
    操作数类型: float2, float2

  步骤8: tmp_7 = tmp_6 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤9: tmp_8 = float3(tmp_7, _10416)
    结果类型: float3
    操作数类型: float2, float

  步骤10: _10465 = tmp_8
    结果类型: float3
    操作数类型: float3

==================================================
第275行运算过程: float3 _10487 = float3(((_10382 + float2(1.5)) + _10398) * _10120, _10416);
运算过程详情:
  步骤1: tmp_0 = float2(1.5)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = _10382 + tmp_0
    结果类型: float2
    操作数类型: float2, float2

  步骤3: tmp_2 = tmp_1 + _10398
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * _10120
    结果类型: float2
    操作数类型: float2, float2

  步骤5: tmp_4 = float3(tmp_3, _10416)
    结果类型: float3
    操作数类型: float2, float

  步骤6: _10487 = tmp_4
    结果类型: float3
    操作数类型: float3

==================================================
第276行运算过程: float _10493 = _10388.x;
运算过程详情:
  步骤1: tmp_0 = _10388.x
    结果类型: float
    操作数类型: float

  步骤2: _10493 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第277行运算过程: float _10500 = _10392.x;
运算过程详情:
  步骤1: tmp_0 = _10392.x
    结果类型: float
    操作数类型: float

  步骤2: _10500 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第278行运算过程: float _10511 = _10392.y;
运算过程详情:
  步骤1: tmp_0 = _10392.y
    结果类型: float
    操作数类型: float

  步骤2: _10511 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第279行运算过程: half _8351 = max(half(mix(0.0, 1.0 - fast::clamp((abs(dot(_9109, _Block1.SunDirection.xyz)) + ((2.0 * _9100) * _9100)) - 1.0, 0.0, 1.0), _Block1.cMicroShadow)), max(half(((((_10388.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10419.xy, uint(rint(_10419.z)), _10004, level(0.0)) * _10493) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10442.xy, uint(rint(_10442.z)), _10004, level(0.0)) * _10500))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10465.xy, uint(rint(_10465.z)), _10004, level(0.0)) * _10493) * _10511)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10487.xy, uint(rint(_10487.z)), _10004, level(0.0)) * _10500) * _10511)) * 0.111111097037792205810546875) * float(all(_17928 > float3(0.0)) && all(_17928 < float3(1.0)))), half(fast::min(1.0, float(half(((((_10177.y * ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10208.xy, uint(rint(_10208.z)), _10113, level(0.0)) * _10282) + (sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10231.xy, uint(rint(_10231.z)), _10113, level(0.0)) * _10289))) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10254.xy, uint(rint(_10254.z)), _10113, level(0.0)) * _10282) * _10300)) + ((sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, _10276.xy, uint(rint(_10276.z)), _10113, level(0.0)) * _10289) * _10300)) * 0.111111097037792205810546875) * float(all(_21138 > float3(0.0)) && all(_21138 < float3(1.0)))))))));
运算过程详情:
  步骤1: tmp_0 = _Block1.SunDirection.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = dot(_9109, tmp_0)
    结果类型: float
    操作数类型: float3, float3

  步骤3: tmp_2 = abs(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = 2.0 * _9100
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_3 * _9100
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_2 + tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = tmp_5 - 1.0
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = fast::clamp(tmp_6, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤9: tmp_8 = 1.0 - tmp_7
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = _Block1.cMicroShadow
    结果类型: float
    操作数类型: float

  步骤11: tmp_10 = mix(0.0, tmp_8, tmp_9)
    结果类型: float
    操作数类型: float, float, float

  步骤12: tmp_11 = half(tmp_10)
    结果类型: half
    操作数类型: float

  步骤13: tmp_12 = _10388.y
    结果类型: float
    操作数类型: float

  步骤14: tmp_13 = _10419.xy
    结果类型: float2
    操作数类型: float2

  步骤15: tmp_14 = _10419.z
    结果类型: float
    操作数类型: float

  步骤16: tmp_15 = rint(tmp_14)
    结果类型: float
    操作数类型: float

  步骤17: tmp_16 = uint(tmp_15)
    结果类型: uint
    操作数类型: float

  步骤18: tmp_17 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤19: tmp_18 = sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, tmp_13, tmp_16, _10004, tmp_17)
    结果类型: float
    操作数类型: unknown, float2, uint, float, float

  步骤20: tmp_19 = tmp_18 * _10493
    结果类型: float
    操作数类型: float, float

  步骤21: tmp_20 = _10442.xy
    结果类型: float2
    操作数类型: float2

  步骤22: tmp_21 = _10442.z
    结果类型: float
    操作数类型: float

  步骤23: tmp_22 = rint(tmp_21)
    结果类型: float
    操作数类型: float

  步骤24: tmp_23 = uint(tmp_22)
    结果类型: uint
    操作数类型: float

  步骤25: tmp_24 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤26: tmp_25 = sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, tmp_20, tmp_23, _10004, tmp_24)
    结果类型: float
    操作数类型: unknown, float2, uint, float, float

  步骤27: tmp_26 = tmp_25 * _10500
    结果类型: float
    操作数类型: float, float

  步骤28: tmp_27 = tmp_19 + tmp_26
    结果类型: float
    操作数类型: float, float

  步骤29: tmp_28 = tmp_12 * tmp_27
    结果类型: float
    操作数类型: float, float

  步骤30: tmp_29 = _10465.xy
    结果类型: float2
    操作数类型: float2

  步骤31: tmp_30 = _10465.z
    结果类型: float
    操作数类型: float

  步骤32: tmp_31 = rint(tmp_30)
    结果类型: float
    操作数类型: float

  步骤33: tmp_32 = uint(tmp_31)
    结果类型: uint
    操作数类型: float

  步骤34: tmp_33 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤35: tmp_34 = sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, tmp_29, tmp_32, _10004, tmp_33)
    结果类型: float
    操作数类型: unknown, float2, uint, float, float

  步骤36: tmp_35 = tmp_34 * _10493
    结果类型: float
    操作数类型: float, float

  步骤37: tmp_36 = tmp_35 * _10511
    结果类型: float
    操作数类型: float, float

  步骤38: tmp_37 = tmp_28 + tmp_36
    结果类型: float
    操作数类型: float, float

  步骤39: tmp_38 = _10487.xy
    结果类型: float2
    操作数类型: float2

  步骤40: tmp_39 = _10487.z
    结果类型: float
    操作数类型: float

  步骤41: tmp_40 = rint(tmp_39)
    结果类型: float
    操作数类型: float

  步骤42: tmp_41 = uint(tmp_40)
    结果类型: uint
    操作数类型: float

  步骤43: tmp_42 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤44: tmp_43 = sShadowMapArraySampler.sample_compare(sShadowMapArraySamplerSmplr, tmp_38, tmp_41, _10004, tmp_42)
    结果类型: float
    操作数类型: unknown, float2, uint, float, float

  步骤45: tmp_44 = tmp_43 * _10500
    结果类型: float
    操作数类型: float, float

  步骤46: tmp_45 = tmp_44 * _10511
    结果类型: float
    操作数类型: float, float

  步骤47: tmp_46 = tmp_37 + tmp_45
    结果类型: float
    操作数类型: float, float

  步骤48: tmp_47 = tmp_46 * 0.111111097037792205810546875
    结果类型: float
    操作数类型: float, float

  步骤49: tmp_48 = all(_17928)
    结果类型: float
    操作数类型: float3

  步骤50: tmp_49 = float(tmp_48)
    结果类型: float
    操作数类型: float

  步骤51: tmp_50 = tmp_47 * tmp_49
    结果类型: float
    操作数类型: float, float

  步骤52: tmp_51 = half(tmp_50)
    结果类型: half
    操作数类型: float

  步骤53: tmp_52 = max(tmp_51)
    结果类型: float
    操作数类型: half

  步骤54: tmp_53 = max(tmp_11, tmp_52)
    结果类型: float
    操作数类型: half, float

  步骤55: _8351 = tmp_53
    结果类型: half
    操作数类型: float

==================================================
第280行运算过程: float3 _8370 = in.IN_WorldPosition.xyz - _Block1.CameraPos.xyz;
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _8370 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第281行运算过程: float3 _8373 = fast::normalize(-_8370);
运算过程详情:
  步骤1: tmp_0 = -_8370
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: _8373 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第282行运算过程: float _8378 = dot(_9109, _8373);
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _8373)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _8378 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第283行运算过程: half3 _10557 = mix(half3(half(0.039999999105930328369140625)), _8315, half3(_18234));
运算过程详情:
  步骤1: tmp_0 = half(0.039999999105930328369140625)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: half

  步骤3: tmp_2 = half3(_18234)
    结果类型: half3
    操作数类型: unknown

  步骤4: tmp_3 = mix(tmp_1, _8315, tmp_2)
    结果类型: half3
    操作数类型: half3, half3, half3

  步骤5: _10557 = tmp_3
    结果类型: half3
    操作数类型: half3

==================================================
第284行运算过程: half3 _10569 = half3(float3(_8315 - (_8315 * _18234)) * float3(0.3183098733425140380859375));
运算过程详情:
  步骤1: tmp_0 = _8315 * _18234
    结果类型: half3
    操作数类型: half3, unknown

  步骤2: tmp_1 = _8315 - tmp_0
    结果类型: half3
    操作数类型: half3, half3

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: half3

  步骤4: tmp_3 = float3(0.3183098733425140380859375)
    结果类型: float3
    操作数类型: float

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float3
    操作数类型: float3, float3

  步骤6: tmp_5 = half3(tmp_4)
    结果类型: half3
    操作数类型: float3

  步骤7: _10569 = tmp_5
    结果类型: half3
    操作数类型: half3

==================================================
第285行运算过程: float3 _10588 = float3(_Block1.EnvInfo.z);
运算过程详情:
  步骤1: tmp_0 = _Block1.EnvInfo.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float

  步骤3: _10588 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第286行运算过程: half3 _8393 = half3(half(0.0));
运算过程详情:
  步骤1: tmp_0 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: half

  步骤3: _8393 = tmp_1
    结果类型: half3
    操作数类型: half3

==================================================
第287行运算过程: uint _8397 = as_type<uint>(_Block1.SHGIParam.w);
运算过程详情:
  步骤1: _8397 = as_type
    结果类型: uint
    操作数类型: unknown

==================================================
第288行运算过程: bool _8401 = (_8397 & 63u) > 0u;
运算过程详情:
  步骤1: _8401 = _8397
    结果类型: bool
    操作数类型: uint

==================================================
第290行运算过程: if (_8401)
  无运算过程
==================================================
第292行运算过程: float3 _8435 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u));
运算过程详情:
  步骤1: tmp_0 = _Block1.PlayerPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = bool3(_8397)
    结果类型: float
    操作数类型: uint

  步骤4: tmp_3 = select(tmp_0, tmp_1, tmp_2)
    结果类型: float
    操作数类型: float3, float3, float

  步骤5: _8435 = tmp_3
    结果类型: float3
    操作数类型: float

==================================================
第294行运算过程: if (_8401)
  无运算过程
==================================================
第297行运算过程: if ((_8397 & 8u) != 0u)
  无运算过程
==================================================
第299行运算过程: float3 _10686 = (in.IN_WorldPosition.xyz + (_8373 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _8373 * 0.100000001490116119384765625
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = tmp_0 + tmp_1
    结果类型: float
    操作数类型: float3, float

  步骤4: tmp_3 = float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)
    结果类型: float3
    操作数类型: float, float, float

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float
    操作数类型: float, float3

  步骤6: _10686 = tmp_4
    结果类型: float3
    操作数类型: float

==================================================
第300行运算过程: float3 _10762 = _10686 - floor(_10686);
运算过程详情:
  步骤1: tmp_0 = floor(_10686)
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _10686 - tmp_0
    结果类型: float3
    操作数类型: float3, float3

  步骤3: _10762 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第301行运算过程: float3 _10789 = _8435 * float3(0.125);
运算过程详情:
  步骤1: tmp_0 = float3(0.125)
    结果类型: float3
    操作数类型: float

  步骤2: tmp_1 = _8435 * tmp_0
    结果类型: float3
    操作数类型: float3, float3

  步骤3: _10789 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第302行运算过程: float _10797 = _10789.x;
运算过程详情:
  步骤1: tmp_0 = _10789.x
    结果类型: float
    操作数类型: float

  步骤2: _10797 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第303行运算过程: float _10799 = floor(_10797);
运算过程详情:
  步骤1: tmp_0 = floor(_10797)
    结果类型: float
    操作数类型: float

  步骤2: _10799 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第305行运算过程: _21191.x = _10799 - 15.0;
运算过程详情:
  步骤1: tmp_0 = _10799 - 15.0
    结果类型: float
    操作数类型: float, float

  步骤2: _21191.x = tmp_0
    操作数类型: float

==================================================
第307行运算过程: if ((_10797 - _10799) > 0.5)
  无运算过程
==================================================
第309行运算过程: float3 _21194 = _21191;
运算过程详情:
  步骤1: _21194 = _21191
    结果类型: float3
    操作数类型: unknown

==================================================
第310行运算过程: _21194.x = _10799 + (-14.0);
运算过程详情:
  步骤1: tmp_0 = -14.0
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _10799 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _21194.x = tmp_1
    操作数类型: float

==================================================
第311行运算过程: _21235 = _21194;
运算过程详情:
  步骤1: _21235 = _21194
    操作数类型: float3

==================================================
第315行运算过程: _21235 = _21191;
运算过程详情:
  步骤1: _21235 = _21191
    操作数类型: unknown

==================================================
第317行运算过程: float _21078 = _10789.y;
运算过程详情:
  步骤1: tmp_0 = _10789.y
    结果类型: float
    操作数类型: float

  步骤2: _21078 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第318行运算过程: float _21079 = floor(_21078);
运算过程详情:
  步骤1: tmp_0 = floor(_21078)
    结果类型: float
    操作数类型: float

  步骤2: _21079 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第319行运算过程: float3 _21198 = _21235;
运算过程详情:
  步骤1: _21198 = _21235
    结果类型: float3
    操作数类型: unknown

==================================================
第320行运算过程: _21198.y = _21079 - 8.0;
运算过程详情:
  步骤1: tmp_0 = _21079 - 8.0
    结果类型: float
    操作数类型: float, float

  步骤2: _21198.y = tmp_0
    操作数类型: float

==================================================
第322行运算过程: if ((_21078 - _21079) > 0.5)
  无运算过程
==================================================
第324行运算过程: float3 _21201 = _21198;
运算过程详情:
  步骤1: _21201 = _21198
    结果类型: float3
    操作数类型: float3

==================================================
第325行运算过程: _21201.y = _21079 + (-7.0);
运算过程详情:
  步骤1: tmp_0 = -7.0
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _21079 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _21201.y = tmp_1
    操作数类型: float

==================================================
第326行运算过程: _21236 = _21201;
运算过程详情:
  步骤1: _21236 = _21201
    操作数类型: float3

==================================================
第330行运算过程: _21236 = _21198;
运算过程详情:
  步骤1: _21236 = _21198
    操作数类型: float3

==================================================
第332行运算过程: float _21100 = _10789.z;
运算过程详情:
  步骤1: tmp_0 = _10789.z
    结果类型: float
    操作数类型: float

  步骤2: _21100 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第333行运算过程: float _21101 = floor(_21100);
运算过程详情:
  步骤1: tmp_0 = floor(_21100)
    结果类型: float
    操作数类型: float

  步骤2: _21101 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第334行运算过程: float3 _21205 = _21236;
运算过程详情:
  步骤1: _21205 = _21236
    结果类型: float3
    操作数类型: unknown

==================================================
第335行运算过程: _21205.z = _21101 - 15.0;
运算过程详情:
  步骤1: tmp_0 = _21101 - 15.0
    结果类型: float
    操作数类型: float, float

  步骤2: _21205.z = tmp_0
    操作数类型: float

==================================================
第337行运算过程: if ((_21100 - _21101) > 0.5)
  无运算过程
==================================================
第339行运算过程: float3 _21208 = _21205;
运算过程详情:
  步骤1: _21208 = _21205
    结果类型: float3
    操作数类型: float3

==================================================
第340行运算过程: _21208.z = _21101 + (-14.0);
运算过程详情:
  步骤1: tmp_0 = -14.0
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _21101 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _21208.z = tmp_1
    操作数类型: float

==================================================
第341行运算过程: _21237 = _21208;
运算过程详情:
  步骤1: _21237 = _21208
    操作数类型: float3

==================================================
第345行运算过程: _21237 = _21205;
运算过程详情:
  步骤1: _21237 = _21205
    操作数类型: float3

==================================================
第347行运算过程: float3 _10822 = _21237 * 8.0;
运算过程详情:
  步骤1: tmp_0 = _21237 * 8.0
    结果类型: float
    操作数类型: unknown, float

  步骤2: _10822 = tmp_0
    结果类型: float3
    操作数类型: float

==================================================
第349行运算过程: if (all(in.IN_WorldPosition.xyz >= _10822) && all(in.IN_WorldPosition.xyz < (_10822 + float3(240.0, 128.0, 240.0))))
  无运算过程
==================================================
第351行运算过程: uint _10704 = (_8397 & 251658240u) >> 24u;
运算过程详情:
  步骤1: _10704 = _8397
    结果类型: uint
    操作数类型: uint

==================================================
第352行运算过程: float _10887 = 3.0 - float((_8397 & 458752u) >> 16u);
运算过程详情:
  步骤1: tmp_0 = float(_8397)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = 3.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _10887 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第354行运算过程: if (_10704 <= 3u)
  无运算过程
==================================================
第356行运算过程: float _10900 = 3.0 - float(_10704);
运算过程详情:
  步骤1: tmp_0 = float(_10704)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = 3.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _10900 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第357行运算过程: float2 _10994 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _10762.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float2, float

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float
    操作数类型: float, float2

  步骤7: _10994 = tmp_5
    结果类型: float2
    操作数类型: float

==================================================
第358行运算过程: float _11001 = _10822.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _11001 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第359行运算过程: float _11005 = ((_11001 - floor(_11001)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_11001)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _11001 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _11005 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第360行运算过程: float _11011 = _10822.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _11011 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第361行运算过程: float _11015 = ((_11011 - floor(_11011)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_11011)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _11011 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _11015 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第362行运算过程: float _11020 = _10994.x;
运算过程详情:
  步骤1: tmp_0 = _10994.x
    结果类型: float
    操作数类型: float

  步骤2: _11020 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第364行运算过程: _17954.x = (_11020 < (_11005 + 0.5)) ? fast::min(_11020, _11005 + 0.49609375) : fast::max(_11020, _11005 + 0.50390625);
运算过程详情:
  步骤1: _17954.x = _11020
    操作数类型: float

==================================================
第365行运算过程: float _11038 = _10994.y;
运算过程详情:
  步骤1: tmp_0 = _10994.y
    结果类型: float
    操作数类型: float

  步骤2: _11038 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第366行运算过程: _17954.z = (_11038 < (_11015 + 0.5)) ? fast::min(_11038, _11015 + 0.49609375) : fast::max(_11038, _11015 + 0.50390625);
运算过程详情:
  步骤1: _17954.z = _11038
    操作数类型: float

==================================================
第367行运算过程: float _11059 = (_10762.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _10762.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: _11059 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第368行运算过程: float _11064 = floor(_11059);
运算过程详情:
  步骤1: tmp_0 = floor(_11059)
    结果类型: float
    操作数类型: float

  步骤2: _11064 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第369行运算过程: uint _11067 = (_11059 < 0.0) ? 63u : uint(_11064);
运算过程详情:
  步骤1: _11067 = _11059
    结果类型: uint
    操作数类型: float

==================================================
第370行运算过程: uint _11070 = _11067 + 1u;
运算过程详情:
  步骤1: tmp_0 = _11067 + 1
    结果类型: int
    操作数类型: uint, int

  步骤2: _11070 = tmp_0
    结果类型: uint
    操作数类型: int

==================================================
第371行运算过程: uint _21301 = (_11070 >= 64u) ? 0u : _11070;
  无运算过程
==================================================
第372行运算过程: float2 _11097 = (float2(float(_11067 & 7u), float(_11067 >> 3u)) + _17954.xz) * 0.125;
运算过程详情:
  步骤1: tmp_0 = float(_11067)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _11097 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第373行运算过程: float _11100 = _11097.x;
运算过程详情:
  步骤1: tmp_0 = _11097.x
    结果类型: float
    操作数类型: float

  步骤2: _11100 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第374行运算过程: float3 _11102 = float3(_11100, _11097.y, _10887);
运算过程详情:
  步骤1: tmp_0 = _11097.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_11100, tmp_0, _10887)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _11102 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第376行运算过程: _17962.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11102.xy, uint(rint(_11102.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _11102.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _11102.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _17962.w = tmp_6
    操作数类型: half

==================================================
第377行运算过程: float3 _11113 = float3(_11100, _11097.y, _10900);
运算过程详情:
  步骤1: tmp_0 = _11097.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_11100, tmp_0, _10900)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _11113 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第378行运算过程: half3 _11118 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11113.xy, uint(rint(_11113.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _11113.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _11113.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _11118 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第379行运算过程: float2 _11135 = (float2(float(_21301 & 7u), float(_21301 >> 3u)) + _17954.xz) * 0.125;
运算过程详情:
  步骤1: tmp_0 = float(_21301)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _11135 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第380行运算过程: float _11138 = _11135.x;
运算过程详情:
  步骤1: tmp_0 = _11135.x
    结果类型: float
    操作数类型: float

  步骤2: _11138 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第381行运算过程: float3 _11140 = float3(_11138, _11135.y, _10887);
运算过程详情:
  步骤1: tmp_0 = _11135.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_11138, tmp_0, _10887)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _11140 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第383行运算过程: _17964.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11140.xy, uint(rint(_11140.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _11140.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _11140.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _17964.w = tmp_6
    操作数类型: half

==================================================
第384行运算过程: float3 _11151 = float3(_11138, _11135.y, _10900);
运算过程详情:
  步骤1: tmp_0 = _11135.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_11138, tmp_0, _10900)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _11151 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第385行运算过程: half3 _11156 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _11151.xy, uint(rint(_11151.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _11151.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _11151.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _11156 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第386行运算过程: half4 _11163 = mix(half4(_11118.x, _11118.y, _11118.z, _17962.w), half4(_11156.x, _11156.y, _11156.z, _17964.w), half4(half(fast::clamp(_11059 - _11064, 0.0, 1.0))));
运算过程详情:
  步骤1: tmp_0 = _11118.x
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = _11118.y
    结果类型: half
    操作数类型: half

  步骤3: tmp_2 = _11118.z
    结果类型: half
    操作数类型: half

  步骤4: tmp_3 = _17962.w
    结果类型: unknown
    操作数类型: unknown

  步骤5: tmp_4 = half4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤6: tmp_5 = _11156.x
    结果类型: half
    操作数类型: half

  步骤7: tmp_6 = _11156.y
    结果类型: half
    操作数类型: half

  步骤8: tmp_7 = _11156.z
    结果类型: half
    操作数类型: half

  步骤9: tmp_8 = _17964.w
    结果类型: unknown
    操作数类型: unknown

  步骤10: tmp_9 = half4(tmp_5, tmp_6, tmp_7, tmp_8)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤11: tmp_10 = _11059 - _11064
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = fast::clamp(tmp_10, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤13: tmp_12 = half(tmp_11)
    结果类型: half
    操作数类型: float

  步骤14: tmp_13 = half4(tmp_12)
    结果类型: half4
    操作数类型: half

  步骤15: tmp_14 = mix(tmp_4, tmp_9, tmp_13)
    结果类型: half4
    操作数类型: half4, half4, half4

  步骤16: _11163 = tmp_14
    结果类型: half4
    操作数类型: half4

==================================================
第387行运算过程: _18297 = clamp((_11163.w * half(32.0)) + half(float(dot(half3(_8373), half3((float3(_11163.xyz) * float3(2.0)) - float3(1.0)))) * 2.0), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = _11163.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = half(32.0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = half3(_8373)
    结果类型: half3
    操作数类型: float3

  步骤5: tmp_4 = _11163.xyz
    结果类型: half3
    操作数类型: half3

  步骤6: tmp_5 = float3(tmp_4)
    结果类型: float3
    操作数类型: half3

  步骤7: tmp_6 = float3(2.0)
    结果类型: float3
    操作数类型: float

  步骤8: tmp_7 = tmp_5 * tmp_6
    结果类型: float3
    操作数类型: float3, float3

  步骤9: tmp_8 = float3(1.0)
    结果类型: float3
    操作数类型: float

  步骤10: tmp_9 = tmp_7 - tmp_8
    结果类型: float3
    操作数类型: float3, float3

  步骤11: tmp_10 = half3(tmp_9)
    结果类型: half3
    操作数类型: float3

  步骤12: tmp_11 = dot(tmp_3, tmp_10)
    结果类型: float
    操作数类型: half3, half3

  步骤13: tmp_12 = float(tmp_11)
    结果类型: float
    操作数类型: float

  步骤14: tmp_13 = tmp_12 * 2.0
    结果类型: float
    操作数类型: float, float

  步骤15: tmp_14 = half(tmp_13)
    结果类型: half
    操作数类型: float

  步骤16: tmp_15 = tmp_2 + tmp_14
    结果类型: half
    操作数类型: half, half

  步骤17: tmp_16 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤18: tmp_17 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤19: tmp_18 = clamp(tmp_15, tmp_16, tmp_17)
    结果类型: half
    操作数类型: half, half, half

  步骤20: _18297 = tmp_18
    操作数类型: half

==================================================
第391行运算过程: float2 _11233 = ((_10762.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _10762.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float2, float

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float
    操作数类型: float, float2

  步骤7: _11233 = tmp_5
    结果类型: float2
    操作数类型: float

==================================================
第392行运算过程: float _11240 = _10822.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _11240 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第393行运算过程: float _11244 = ((_11240 - floor(_11240)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_11240)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _11240 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _11244 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第394行运算过程: float _11250 = _10822.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _10822.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _11250 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第395行运算过程: float _11254 = ((_11250 - floor(_11250)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_11250)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _11250 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _11254 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第396行运算过程: float _11259 = _11233.x;
运算过程详情:
  步骤1: tmp_0 = _11233.x
    结果类型: float
    操作数类型: float

  步骤2: _11259 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第398行运算过程: _17977.x = (_11259 < (_11244 + 0.5)) ? fast::min(_11259, _11244 + 0.49609375) : fast::max(_11259, _11244 + 0.50390625);
运算过程详情:
  步骤1: _17977.x = _11259
    操作数类型: float

==================================================
第399行运算过程: float _11277 = _11233.y;
运算过程详情:
  步骤1: tmp_0 = _11233.y
    结果类型: float
    操作数类型: float

  步骤2: _11277 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第400行运算过程: _17977.z = (_11277 < (_11254 + 0.5)) ? fast::min(_11277, _11254 + 0.49609375) : fast::max(_11277, _11254 + 0.50390625);
运算过程详情:
  步骤1: _17977.z = _11277
    操作数类型: float

==================================================
第401行运算过程: float _11298 = (_10762.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _10762.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: _11298 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第402行运算过程: float _11303 = floor(_11298);
运算过程详情:
  步骤1: tmp_0 = floor(_11298)
    结果类型: float
    操作数类型: float

  步骤2: _11303 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第403行运算过程: uint _11306 = (_11298 < 0.0) ? 63u : uint(_11303);
运算过程详情:
  步骤1: _11306 = _11298
    结果类型: uint
    操作数类型: float

==================================================
第404行运算过程: uint _11309 = _11306 + 1u;
运算过程详情:
  步骤1: tmp_0 = _11306 + 1
    结果类型: int
    操作数类型: uint, int

  步骤2: _11309 = tmp_0
    结果类型: uint
    操作数类型: int

==================================================
第405行运算过程: uint _21300 = (_11309 >= 64u) ? 0u : _11309;
  无运算过程
==================================================
第406行运算过程: float3 _11340 = float3((float2(float(_11306 & 7u), float(_11306 >> 3u)) + _17977.xz) * 0.125, _10887);
运算过程详情:
  步骤1: tmp_0 = float(_11306)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float2

  步骤4: _11340 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第407行运算过程: float3 _11365 = float3((float2(float(_21300 & 7u), float(_21300 >> 3u)) + _17977.xz) * 0.125, _10887);
运算过程详情:
  步骤1: tmp_0 = float(_21300)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float2

  步骤4: _11365 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第408行运算过程: _18297 = half(mix(float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11340.xy, uint(rint(_11340.z)), level(0.0)).x)), float(half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _11365.xy, uint(rint(_11365.z)), level(0.0)).x)), fast::clamp(_11298 - _11303, 0.0, 1.0))) * half(32.0);
运算过程详情:
  步骤1: tmp_0 = _11340.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _11340.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: tmp_7 = float(tmp_6)
    结果类型: float
    操作数类型: half

  步骤9: tmp_8 = mix(tmp_7)
    结果类型: float
    操作数类型: float

  步骤10: tmp_9 = half(tmp_8)
    结果类型: half
    操作数类型: float

  步骤11: _18297 = tmp_9
    操作数类型: half

==================================================
第410行运算过程: float3 _11404 = (((in.IN_WorldPosition.xyz - _10822) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = tmp_0 - _10822
    结果类型: float3
    操作数类型: float3, float3

  步骤3: tmp_2 = float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)
    结果类型: float3
    操作数类型: float, float, float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: tmp_4 = tmp_3 * 2.0
    结果类型: float
    操作数类型: float3, float

  步骤6: tmp_5 = float3(1.0)
    结果类型: float3
    操作数类型: float

  步骤7: tmp_6 = tmp_4 - tmp_5
    结果类型: float
    操作数类型: float, float3

  步骤8: _11404 = tmp_6
    结果类型: float3
    操作数类型: float

==================================================
第411行运算过程: float3 _11407 = _11404 * _11404;
运算过程详情:
  步骤1: tmp_0 = _11404 * _11404
    结果类型: float3
    操作数类型: float3, float3

  步骤2: _11407 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第412行运算过程: float3 _11410 = _11407 * _11407;
运算过程详情:
  步骤1: tmp_0 = _11407 * _11407
    结果类型: float3
    操作数类型: float3, float3

  步骤2: _11410 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第414行运算过程: if ((!((_8397 & 4u) != 0u)) && ((_8397 & 32768u) > 0u))
  无运算过程
==================================================
第416行运算过程: _18303 = half(mix(float(_18297), 1.0, fast::clamp(fast::max(_11410.x, fast::max(_11410.y, _11410.z)), 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = float(_18297)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = _11410.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _11410.y
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = _11410.z
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = fast::max(tmp_2, tmp_3)
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::max(tmp_1, tmp_4)
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤8: tmp_7 = mix(tmp_0, 1.0, tmp_6)
    结果类型: float
    操作数类型: float, float, float

  步骤9: tmp_8 = half(tmp_7)
    结果类型: half
    操作数类型: float

  步骤10: _18303 = tmp_8
    操作数类型: half

==================================================
第420行运算过程: _18303 = _18297;
运算过程详情:
  步骤1: _18303 = _18297
    操作数类型: unknown

==================================================
第422行运算过程: _18305 = _18303;
运算过程详情:
  步骤1: _18305 = _18303
    操作数类型: unknown

==================================================
第426行运算过程: _18305 = _9019;
运算过程详情:
  步骤1: _18305 = _9019
    操作数类型: half

==================================================
第428行运算过程: _18304 = _18305;
运算过程详情:
  步骤1: _18304 = _18305
    操作数类型: unknown

==================================================
第432行运算过程: _18304 = _9019;
运算过程详情:
  步骤1: _18304 = _9019
    操作数类型: half

==================================================
第434行运算过程: float _11467 = _Block1.SHAOParam.z * _Block1.SHAOParam.z;
运算过程详情:
  步骤1: tmp_0 = _Block1.SHAOParam.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.SHAOParam.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _11467 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第435行运算过程: float3 _11470 = in.IN_WorldPosition.xyz - _8435;
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = tmp_0 - _8435
    结果类型: float3
    操作数类型: float3, float3

  步骤3: _11470 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第436行运算过程: float _11479 = fast::clamp((_11467 - dot(_11470, _11470)) / _11467, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = dot(_11470, _11470)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = _11467 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 / _11467
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _11479 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第437行运算过程: _18306 = half(1.0 - fast::clamp((1.0 - float(half(fast::clamp(1.0 - float(half(powr(float(half(fast::clamp(1.0 - float(_18304), 0.0, 1.0))), fast::max(mix(6.0, 0.0, fast::clamp(_Block1.SHGIParam2.w, 0.0, 1.0)), 0.5)))), 0.0, 1.0)))) * float(half(mix(_Block1.SHAOParam.x, _Block1.SHAOParam.y, 1.0 - (_11479 * _11479)))), 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = float(_18304)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = float(tmp_3)
    结果类型: float
    操作数类型: half

  步骤6: tmp_5 = _Block1.SHGIParam2.w
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤8: tmp_7 = mix(6.0, 0.0, tmp_6)
    结果类型: float
    操作数类型: float, float, float

  步骤9: tmp_8 = fast::max(tmp_7, 0.5)
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = powr(tmp_4, tmp_8)
    结果类型: float
    操作数类型: float, float

  步骤11: tmp_10 = half(tmp_9)
    结果类型: half
    操作数类型: float

  步骤12: tmp_11 = float(tmp_10)
    结果类型: float
    操作数类型: half

  步骤13: tmp_12 = 1.0 - tmp_11
    结果类型: float
    操作数类型: float, float

  步骤14: tmp_13 = fast::clamp(tmp_12, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤15: tmp_14 = half(tmp_13)
    结果类型: half
    操作数类型: float

  步骤16: tmp_15 = float(tmp_14)
    结果类型: float
    操作数类型: half

  步骤17: tmp_16 = 1.0 - tmp_15
    结果类型: float
    操作数类型: float, float

  步骤18: tmp_17 = _Block1.SHAOParam.x
    结果类型: float
    操作数类型: float

  步骤19: tmp_18 = _Block1.SHAOParam.y
    结果类型: float
    操作数类型: float

  步骤20: tmp_19 = _11479 * _11479
    结果类型: float
    操作数类型: float, float

  步骤21: tmp_20 = 1.0 - tmp_19
    结果类型: float
    操作数类型: float, float

  步骤22: tmp_21 = mix(tmp_17, tmp_18, tmp_20)
    结果类型: float
    操作数类型: float, float, float

  步骤23: tmp_22 = half(tmp_21)
    结果类型: half
    操作数类型: float

  步骤24: tmp_23 = float(tmp_22)
    结果类型: float
    操作数类型: half

  步骤25: tmp_24 = tmp_16 * tmp_23
    结果类型: float
    操作数类型: float, float

  步骤26: tmp_25 = fast::clamp(tmp_24, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤27: tmp_26 = 1.0 - tmp_25
    结果类型: float
    操作数类型: float, float

  步骤28: tmp_27 = half(tmp_26)
    结果类型: half
    操作数类型: float

  步骤29: _18306 = tmp_27
    操作数类型: half

==================================================
第441行运算过程: _18306 = _9019;
运算过程详情:
  步骤1: _18306 = _9019
    操作数类型: half

==================================================
第444行运算过程: if (!((_8397 & 64u) > 0u))
  无运算过程
==================================================
第446行运算过程: _18330 = _8346 * _18306;
运算过程详情:
  步骤1: tmp_0 = _8346 * _18306
    结果类型: half
    操作数类型: half, unknown

  步骤2: _18330 = tmp_0
    操作数类型: half

==================================================
第450行运算过程: _18330 = _8346;
运算过程详情:
  步骤1: _18330 = _8346
    操作数类型: half

==================================================
第452行运算过程: _18329 = _18330;
运算过程详情:
  步骤1: _18329 = _18330
    操作数类型: unknown

==================================================
第456行运算过程: _18329 = _8346;
运算过程详情:
  步骤1: _18329 = _8346
    操作数类型: half

==================================================
第458行运算过程: float3 _11517 = float3(half3(_8373));
运算过程详情:
  步骤1: tmp_0 = half3(_8373)
    结果类型: half3
    操作数类型: float3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: _11517 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第459行运算过程: float3 _11600 = _Block1.CameraPos.xyz + (fast::normalize(float3(_Block1.SunDirection.x, fast::min(_Block1.SunDirection.y, 1.0), _Block1.SunDirection.z)) * 200000.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.SunDirection.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _Block1.SunDirection.y
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = fast::min(tmp_2, 1.0)
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = _Block1.SunDirection.z
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = float3(tmp_1, tmp_3, tmp_4)
    结果类型: float3
    操作数类型: float, float, float

  步骤7: tmp_6 = fast::normalize(tmp_5)
    结果类型: float3
    操作数类型: float3

  步骤8: tmp_7 = tmp_6 * 200000.0
    结果类型: float
    操作数类型: float3, float

  步骤9: tmp_8 = tmp_0 + tmp_7
    结果类型: float
    操作数类型: float3, float

  步骤10: _11600 = tmp_8
    结果类型: float3
    操作数类型: float

==================================================
第460行运算过程: float3 _11604 = reflect(-_11517, _9109);
运算过程详情:
  步骤1: tmp_0 = -_11517
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = reflect(tmp_0, _9109)
    结果类型: float3
    操作数类型: float3, float3

  步骤3: _11604 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第461行运算过程: float3 _11611 = (_11604 * dot(_11600, _11604)) - _11600;
运算过程详情:
  步骤1: tmp_0 = dot(_11600, _11604)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = _11604 * tmp_0
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = tmp_1 - _11600
    结果类型: float
    操作数类型: float, float3

  步骤4: _11611 = tmp_2
    结果类型: float3
    操作数类型: float

==================================================
第462行运算过程: float3 _11622 = fast::normalize(_11600 + (_11611 * fast::clamp(4500.0 / length(_11611), 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = length(_11611)
    结果类型: float
    操作数类型: float3

  步骤2: tmp_1 = 4500.0 / tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: tmp_3 = _11611 * tmp_2
    结果类型: float
    操作数类型: float3, float

  步骤5: tmp_4 = _11600 + tmp_3
    结果类型: float
    操作数类型: float3, float

  步骤6: tmp_5 = fast::normalize(tmp_4)
    结果类型: float
    操作数类型: float

  步骤7: _11622 = tmp_5
    结果类型: float3
    操作数类型: float

==================================================
第463行运算过程: half _11536 = clamp(half(dot(_9109, _11622)), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _11622)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = clamp(tmp_1, tmp_2, tmp_3)
    结果类型: half
    操作数类型: half, half, half

  步骤6: _11536 = tmp_4
    结果类型: half
    操作数类型: half

==================================================
第464行运算过程: float _11560 = float(half(fast::max(0.119999997317790985107421875, float(_18272))));
运算过程详情:
  步骤1: tmp_0 = float(_18272)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = fast::max(0.119999997317790985107421875, tmp_0)
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: _11560 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第465行运算过程: float _11629 = fast::max(0.00999999977648258209228515625, fast::clamp((4.125 * _11560) - 0.319999992847442626953125, 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = 4.125 * _11560
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 - 0.319999992847442626953125
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: tmp_3 = fast::max(0.00999999977648258209228515625, tmp_2)
    结果类型: float
    操作数类型: float, float

  步骤5: _11629 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第466行运算过程: float _11919 = float(_11536);
运算过程详情:
  步骤1: tmp_0 = float(_11536)
    结果类型: float
    操作数类型: half

  步骤2: _11919 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第467行运算过程: float _11925 = dot(_9109, _11517);
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _11517)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _11925 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第468行运算过程: float3 _11940 = fast::normalize(_11517 + _11622);
运算过程详情:
  步骤1: tmp_0 = _11517 + _11622
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: _11940 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第469行运算过程: float _11945 = fast::clamp(dot(_9109, _11940), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _11940)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: _11945 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第470行运算过程: float _11951 = fast::clamp(dot(_11517, _11940), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = dot(_11517, _11940)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: _11951 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第471行运算过程: float _11736 = fast::clamp(abs(fast::clamp(_11925, 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = fast::clamp(_11925, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_1 + 9.9999997473787516355514526367188
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::clamp(tmp_2)
    结果类型: float
    操作数类型: float

  步骤5: _11736 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第472行运算过程: half4 _11959 = half4(half(0.60000002384185791015625));
运算过程详情:
  步骤1: tmp_0 = half(0.60000002384185791015625)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half4(tmp_0)
    结果类型: half4
    操作数类型: half

  步骤3: _11959 = tmp_1
    结果类型: half4
    操作数类型: half4

==================================================
第473行运算过程: half4 _11967 = _11959 * _11959;
运算过程详情:
  步骤1: tmp_0 = _11959 * _11959
    结果类型: half4
    操作数类型: half4, half4

  步骤2: _11967 = tmp_0
    结果类型: half4
    操作数类型: half4

==================================================
第474行运算过程: half4 _11970 = half4(half(1.0)) - _11967;
运算过程详情:
  步骤1: tmp_0 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half4(tmp_0)
    结果类型: half4
    操作数类型: half

  步骤3: tmp_2 = tmp_1 - _11967
    结果类型: half4
    操作数类型: half4, half4

  步骤4: _11970 = tmp_2
    结果类型: half4
    操作数类型: half4

==================================================
第475行运算过程: half4 _11973 = half4(half(1.0)) + _11967;
运算过程详情:
  步骤1: tmp_0 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half4(tmp_0)
    结果类型: half4
    操作数类型: half

  步骤3: tmp_2 = tmp_1 + _11967
    结果类型: half4
    操作数类型: half4, half4

  步骤4: _11973 = tmp_2
    结果类型: half4
    操作数类型: half4

==================================================
第476行运算过程: half4 _11975 = _11959 * half(2.0);
运算过程详情:
  步骤1: tmp_0 = half(2.0)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = _11959 * tmp_0
    结果类型: half
    操作数类型: half4, half

  步骤3: _11975 = tmp_1
    结果类型: half4
    操作数类型: half

==================================================
第477行运算过程: half4 _11982 = half4(half(1.5));
运算过程详情:
  步骤1: tmp_0 = half(1.5)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half4(tmp_0)
    结果类型: half4
    操作数类型: half

  步骤3: _11982 = tmp_1
    结果类型: half4
    操作数类型: half4

==================================================
第478行运算过程: float _11756 = exp2((((-5.554729938507080078125) * _11951) - 6.9831600189208984375) * _11951);
运算过程详情:
  步骤1: tmp_0 = -5.554729938507080078125
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * _11951
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 6.9831600189208984375
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _11951
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = exp2(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: _11756 = tmp_4
    结果类型: float
    操作数类型: float

==================================================
第479行运算过程: float _11763 = _11756 + ((1.0 - _11756) * 0.039999999105930328369140625);
运算过程详情:
  步骤1: tmp_0 = 1.0 - _11756
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * 0.039999999105930328369140625
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = _11756 + tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _11763 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第480行运算过程: half _11764 = half(0.699999988079071044921875);
运算过程详情:
  步骤1: tmp_0 = half(0.699999988079071044921875)
    结果类型: half
    操作数类型: float

  步骤2: _11764 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第481行运算过程: half _11768 = half(float(_11764) + 0.100000001490116119384765625);
运算过程详情:
  步骤1: tmp_0 = float(_11764)
    结果类型: float
    操作数类型: half

  步骤2: tmp_1 = tmp_0 + 0.100000001490116119384765625
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: _11768 = tmp_2
    结果类型: half
    操作数类型: half

==================================================
第482行运算过程: half _11772 = _9199 - _8351;
运算过程详情:
  步骤1: tmp_0 = _9199 - _8351
    结果类型: half
    操作数类型: half, half

  步骤2: _11772 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第483行运算过程: half _11777 = _9199 + _11768;
运算过程详情:
  步骤1: tmp_0 = _9199 + _11768
    结果类型: half
    操作数类型: half, half

  步骤2: _11777 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第484行运算过程: half _11790 = _9199 + _11764;
运算过程详情:
  步骤1: tmp_0 = _9199 + _11764
    结果类型: half
    操作数类型: half, half

  步骤2: _11790 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第485行运算过程: half3 _11812 = half3((float3(half3(_Block1.SunColor.xyz)) * _11919) * float(clamp(((_11772 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_11536 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
运算过程详情:
  步骤1: tmp_0 = _Block1.SunColor.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float3

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: half3

  步骤4: tmp_3 = tmp_2 * _11919
    结果类型: float
    操作数类型: float3, float

  步骤5: tmp_4 = _11772 + _11768
    结果类型: half
    操作数类型: half, half

  步骤6: tmp_5 = tmp_4 / _11777
    结果类型: half
    操作数类型: half, half

  步骤7: tmp_6 = tmp_5 * _11777
    结果类型: half
    操作数类型: half, half

  步骤8: tmp_7 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤9: tmp_8 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤10: tmp_9 = clamp(tmp_6, tmp_7, tmp_8)
    结果类型: half
    操作数类型: half, half, half

  步骤11: tmp_10 = _11536 + _11764
    结果类型: half
    操作数类型: half, half

  步骤12: tmp_11 = tmp_10 / _11790
    结果类型: half
    操作数类型: half, half

  步骤13: tmp_12 = tmp_11 * _11790
    结果类型: half
    操作数类型: half, half

  步骤14: tmp_13 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤15: tmp_14 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤16: tmp_15 = clamp(tmp_12, tmp_13, tmp_14)
    结果类型: half
    操作数类型: half, half, half

  步骤17: tmp_16 = tmp_9 * tmp_15
    结果类型: half
    操作数类型: half, half

  步骤18: tmp_17 = float(tmp_16)
    结果类型: float
    操作数类型: half

  步骤19: tmp_18 = tmp_3 * tmp_17
    结果类型: float
    操作数类型: float, float

  步骤20: tmp_19 = half3(tmp_18)
    结果类型: half3
    操作数类型: float

  步骤21: _11812 = tmp_19
    结果类型: half3
    操作数类型: half3

==================================================
第486行运算过程: half _11815 = half(10.0);
运算过程详情:
  步骤1: tmp_0 = half(10.0)
    结果类型: half
    操作数类型: float

  步骤2: _11815 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第487行运算过程: half _11827 = clamp(dot(half3(fast::normalize(_11622 + _11517)), _9064), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = _11622 + _11517
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = half3(tmp_1)
    结果类型: half3
    操作数类型: float3

  步骤4: tmp_3 = dot(tmp_2, _9064)
    结果类型: float
    操作数类型: half3, half3

  步骤5: tmp_4 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤7: tmp_6 = clamp(tmp_3, tmp_4, tmp_5)
    结果类型: float
    操作数类型: float, half, half

  步骤8: _11827 = tmp_6
    结果类型: half
    操作数类型: float

==================================================
第488行运算过程: float _11858 = float(_11815) * 0.5;
运算过程详情:
  步骤1: tmp_0 = float(_11815)
    结果类型: float
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * 0.5
    结果类型: float
    操作数类型: float, float

  步骤3: _11858 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第489行运算过程: float _11862 = float(_9251 + _11815);
运算过程详情:
  步骤1: tmp_0 = _9251 + _11815
    结果类型: half
    操作数类型: half, half

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: _11862 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第490行运算过程: float _12049 = _11560 * _11560;
运算过程详情:
  步骤1: tmp_0 = _11560 * _11560
    结果类型: float
    操作数类型: float, float

  步骤2: _12049 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第491行运算过程: float _12062 = _12049 / (((((_11945 * _12049) * _12049) - _11945) * _11945) + 1.0);
运算过程详情:
  步骤1: tmp_0 = _11945 * _12049
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * _12049
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - _11945
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _11945
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_3 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _12049 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _12062 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第492行运算过程: float _12080 = _12049 * _12049;
运算过程详情:
  步骤1: tmp_0 = _12049 * _12049
    结果类型: float
    操作数类型: float, float

  步骤2: _12080 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第493行运算过程: float _12108 = float(half(9.9956989288330078125e-05));
运算过程详情:
  步骤1: tmp_0 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: _12108 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第494行运算过程: float3 _12025 = float3(_10557);
运算过程详情:
  步骤1: tmp_0 = float3(_10557)
    结果类型: float3
    操作数类型: half3

  步骤2: _12025 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第495行运算过程: float3 _12120 = float3(fast::clamp(50.0 * _12025.y, 0.0, 1.0)) - _12025;
运算过程详情:
  步骤1: tmp_0 = _12025.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = 50.0 * tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: tmp_3 = float3(tmp_2)
    结果类型: float3
    操作数类型: float

  步骤5: tmp_4 = tmp_3 - _12025
    结果类型: float3
    操作数类型: float3, float3

  步骤6: _12120 = tmp_4
    结果类型: float3
    操作数类型: float3

==================================================
第496行运算过程: float3 _8521 = ((_9698 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_11827 * _11827)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_11517, _11622))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_11763 * _11763) * float(dot(_11812, _8303)), 0.0, 1.0)))) * _11812) * float3(_11629))) + (((float3(_11812) * ((_12025 + (_12120 * _11756)) * (fast::min(1000.0, (_12062 * _12062) * 0.3183098733425140380859375) * (1.0 / fast::max((_11736 + sqrt((_11736 * (_11736 - (_11736 * _12080))) + _12080)) * (_11919 + sqrt((_11919 * (_11919 - (_11919 * _12080))) + _12080)), _12108))))) * _11629) * float(_11772))) * _10588;
运算过程详情:
  步骤1: tmp_0 = _11827 * _11827
    结果类型: half
    操作数类型: half, half

  步骤2: tmp_1 = _9199 - tmp_0
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: tmp_3 = fast::max(tmp_2, 0.0078125)
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = float(tmp_4)
    结果类型: float
    操作数类型: half

  步骤7: tmp_6 = powr(tmp_5, _11858)
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = _11862 * tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = tmp_7 * 0.15915493667125701904296875
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤11: tmp_10 = half4(tmp_9)
    结果类型: half4
    操作数类型: half

  步骤12: tmp_11 = max(tmp_10)
    结果类型: float
    操作数类型: half4

  步骤13: tmp_12 = powr(tmp_11)
    结果类型: float
    操作数类型: float

  步骤14: tmp_13 = _11970 / tmp_12
    结果类型: float
    操作数类型: half4, float

  步骤15: tmp_14 = float4(tmp_13)
    结果类型: float4
    操作数类型: float

  步骤16: tmp_15 = half4(tmp_14)
    结果类型: half4
    操作数类型: float4

  步骤17: tmp_16 = float(tmp_15)
    结果类型: float
    操作数类型: half4

  步骤18: tmp_17 = tmp_8 * tmp_16
    结果类型: float
    操作数类型: float, float

  步骤19: tmp_18 = half(tmp_17)
    结果类型: half
    操作数类型: float

  步骤20: tmp_19 = half3(tmp_18)
    结果类型: half3
    操作数类型: half

  步骤21: tmp_20 = mix(_9179, tmp_19)
    结果类型: half3
    操作数类型: half3, half3

  步骤22: tmp_21 = float3(tmp_20)
    结果类型: float3
    操作数类型: half3

  步骤23: tmp_22 = _9698 + tmp_21
    结果类型: float3
    操作数类型: float3, float3

  步骤24: _8521 = tmp_22
    结果类型: float3
    操作数类型: float3

==================================================
第497行运算过程: uint _12171 = uint(_Block1.LightDataBuffer[0].x);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = uint(tmp_0)
    结果类型: uint
    操作数类型: float4

  步骤3: _12171 = tmp_1
    结果类型: uint
    操作数类型: uint

==================================================
第504行运算过程: _19825 = _18526;
运算过程详情:
  步骤1: _19825 = _18526
    操作数类型: half3

==================================================
第505行运算过程: _19755 = _19185;
运算过程详情:
  步骤1: _19755 = _19185
    操作数类型: float3

==================================================
第506行运算过程: _19720 = _19185;
运算过程详情:
  步骤1: _19720 = _19185
    操作数类型: float3

==================================================
第507行运算过程: _19685 = _19185;
运算过程详情:
  步骤1: _19685 = _19185
    操作数类型: float3

==================================================
第508行运算过程: _18431 = _9698;
运算过程详情:
  步骤1: _18431 = _9698
    操作数类型: float3

==================================================
第509行运算过程: _18429 = _9179;
运算过程详情:
  步骤1: _18429 = _9179
    操作数类型: half3

==================================================
第524行运算过程: for (uint _18428 = 0u; _18428 < _12171; _19825 = _20953, _19790 = _20938, _19755 = _20923, _19720 = _20908, _19685 = _20893, _19613 = _20863, _19577 = _20848, _19485 = _20833, _18431 = _19965, _18429 = _19923, _18428++)
运算过程详情:
  步骤1: for (uint _18428 = 0
    操作数类型: int

==================================================
第526行运算过程: uint _12181 = _18428 * 4u;
运算过程详情:
  步骤1: tmp_0 = _18428 * 4
    结果类型: int
    操作数类型: unknown, int

  步骤2: _12181 = tmp_0
    结果类型: uint
    操作数类型: int

==================================================
第527行运算过程: int _12188 = int(_12181 + 1u);
运算过程详情:
  步骤1: tmp_0 = _12181 + 1
    结果类型: int
    操作数类型: uint, int

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: int

  步骤3: _12188 = tmp_1
    结果类型: int
    操作数类型: int

==================================================
第528行运算过程: int _12195 = int(_12181 + 2u);
运算过程详情:
  步骤1: tmp_0 = _12181 + 2
    结果类型: int
    操作数类型: uint, int

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: int

  步骤3: _12195 = tmp_1
    结果类型: int
    操作数类型: int

==================================================
第529行运算过程: int _12202 = int(_12181 + 3u);
运算过程详情:
  步骤1: tmp_0 = _12181 + 3
    结果类型: int
    操作数类型: uint, int

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: int

  步骤3: _12202 = tmp_1
    结果类型: int
    操作数类型: int

==================================================
第530行运算过程: int _12209 = int(_12181 + 4u);
运算过程详情:
  步骤1: tmp_0 = _12181 + 4
    结果类型: int
    操作数类型: uint, int

  步骤2: tmp_1 = int(tmp_0)
    结果类型: int
    操作数类型: int

  步骤3: _12209 = tmp_1
    结果类型: int
    操作数类型: int

==================================================
第531行运算过程: uint _12298 = as_type<uint>(_Block1.LightDataBuffer[_12209].x);
运算过程详情:
  步骤1: _12298 = as_type
    结果类型: uint
    操作数类型: unknown

==================================================
第532行运算过程: if (!((_12298 & 2097152u) == 2097152u))
  无运算过程
==================================================
第534行运算过程: _20953 = _19825;
运算过程详情:
  步骤1: _20953 = _19825
    操作数类型: unknown

==================================================
第535行运算过程: _20938 = _19790;
运算过程详情:
  步骤1: _20938 = _19790
    操作数类型: unknown

==================================================
第536行运算过程: _20923 = _19755;
运算过程详情:
  步骤1: _20923 = _19755
    操作数类型: unknown

==================================================
第537行运算过程: _20908 = _19720;
运算过程详情:
  步骤1: _20908 = _19720
    操作数类型: unknown

==================================================
第538行运算过程: _20893 = _19685;
运算过程详情:
  步骤1: _20893 = _19685
    操作数类型: unknown

==================================================
第539行运算过程: _20863 = _19613;
运算过程详情:
  步骤1: _20863 = _19613
    操作数类型: unknown

==================================================
第540行运算过程: _20848 = _19577;
运算过程详情:
  步骤1: _20848 = _19577
    操作数类型: unknown

==================================================
第541行运算过程: _20833 = _19485;
运算过程详情:
  步骤1: _20833 = _19485
    操作数类型: unknown

==================================================
第542行运算过程: _19965 = _18431;
运算过程详情:
  步骤1: _19965 = _18431
    操作数类型: unknown

==================================================
第543行运算过程: _19923 = _18429;
运算过程详情:
  步骤1: _19923 = _18429
    操作数类型: unknown

==================================================
第546行运算过程: uint _12309 = _12298 & 196608u;
运算过程详情:
  步骤1: _12309 = _12298
    结果类型: uint
    操作数类型: uint

==================================================
第555行运算过程: if (_12309 == 196608u)
  无运算过程
==================================================
第557行运算过程: float3 _12360 = -_Block1.LightDataBuffer[_12202].xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = -tmp_0
    结果类型: float4
    操作数类型: float4

  步骤3: _12360 = tmp_1
    结果类型: float3
    操作数类型: float4

==================================================
第558行运算过程: float3 _12378 = in.IN_WorldPosition.xyz - (_Block1.LightDataBuffer[_12188].xyz + (_12360 * (dot(in.IN_WorldPosition.xyz - _Block1.LightDataBuffer[_12188].xyz, _12360) / dot(_12360, _12360))));
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float3
    操作数类型: float3, float4

  步骤4: _12378 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第559行运算过程: float _12381 = dot(_12378, _12378);
运算过程详情:
  步骤1: tmp_0 = dot(_12378, _12378)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _12381 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第561行运算过程: if (_12381 > (_Block1.LightDataBuffer[_12209].y * _Block1.LightDataBuffer[_12209].y))
  无运算过程
==================================================
第563行运算过程: float _12392 = sqrt(_12381) - _Block1.LightDataBuffer[_12209].y;
运算过程详情:
  步骤1: tmp_0 = sqrt(_12381)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float4

  步骤4: _12392 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第564行运算过程: float _12395 = _12392 * _12392;
运算过程详情:
  步骤1: tmp_0 = _12392 * _12392
    结果类型: float
    操作数类型: float, float

  步骤2: _12395 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第565行运算过程: float _12398 = _12395 * abs(_Block1.LightDataBuffer[_12188].w);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = _12395 * tmp_1
    结果类型: float
    操作数类型: float, float4

  步骤4: _12398 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第566行运算过程: float _12404 = fast::clamp(1.0 - (_12398 * _12398), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _12398 * _12398
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: _12404 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第567行运算过程: _19350 = fast::min(100.0, (_12404 * _12404) / (_12395 + 1.0));
运算过程详情:
  步骤1: tmp_0 = _12404 * _12404
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = _12395 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_0 / tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::min(100.0, tmp_2)
    结果类型: float
    操作数类型: float, float

  步骤5: _19350 = tmp_3
    操作数类型: float

==================================================
第571行运算过程: _19350 = 1.0;
运算过程详情:
  步骤1: _19350 = 1.0
    操作数类型: float

==================================================
第573行运算过程: _19821 = half3(_Block1.LightDataBuffer[_12195].xyz);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float4

  步骤3: _19821 = tmp_1
    操作数类型: half3

==================================================
第574行运算过程: _19786 = _19350;
运算过程详情:
  步骤1: _19786 = _19350
    操作数类型: unknown

==================================================
第575行运算过程: _19751 = _12360;
运算过程详情:
  步骤1: _19751 = _12360
    操作数类型: float3

==================================================
第576行运算过程: _19716 = _11517;
运算过程详情:
  步骤1: _19716 = _11517
    操作数类型: float3

==================================================
第577行运算过程: _19681 = _9109;
运算过程详情:
  步骤1: _19681 = _9109
    操作数类型: float3

==================================================
第578行运算过程: _19609 = 0;
运算过程详情:
  步骤1: _19609 = 0
    操作数类型: int

==================================================
第579行运算过程: _19573 = abs(_Block1.LightDataBuffer[_12195].w);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float4
    操作数类型: float4

  步骤3: _19573 = tmp_1
    操作数类型: float4

==================================================
第580行运算过程: _19481 = clamp(half(dot(_9109, _12360)), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _12360)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = clamp(tmp_1, tmp_2, tmp_3)
    结果类型: half
    操作数类型: half, half, half

  步骤6: _19481 = tmp_4
    操作数类型: half

==================================================
第592行运算过程: if (_12309 == 0u)
  无运算过程
==================================================
第594行运算过程: uint _12741 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
运算过程详情:
  步骤1: _12741 = as_type
    结果类型: uint
    操作数类型: unknown

==================================================
第595行运算过程: float _12858 = float((_12741 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
运算过程详情:
  步骤1: tmp_0 = float(_12741)
    结果类型: float
    操作数类型: uint

  步骤2: _12858 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第596行运算过程: float3 _12599 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: _12599 = tmp_0
    结果类型: float3
    操作数类型: float4

==================================================
第597行运算过程: float _12602 = dot(_12599, _12599);
运算过程详情:
  步骤1: tmp_0 = dot(_12599, _12599)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _12602 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第598行运算过程: float3 _12606 = _12599 * rsqrt(_12602);
运算过程详情:
  步骤1: tmp_0 = rsqrt(_12602)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _12599 * tmp_0
    结果类型: float
    操作数类型: float3, float

  步骤3: _12606 = tmp_1
    结果类型: float3
    操作数类型: float

==================================================
第599行运算过程: float _12613 = _12602 * abs(_Block1.LightDataBuffer[_12188].w);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = _12602 * tmp_1
    结果类型: float
    操作数类型: float, float4

  步骤4: _12613 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第600行运算过程: float _12620 = fast::clamp(1.0 - (_12613 * _12613), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _12613 * _12613
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: _12620 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第601行运算过程: float _12631 = _12620 * _12620;
运算过程详情:
  步骤1: tmp_0 = _12620 * _12620
    结果类型: float
    操作数类型: float, float

  步骤2: _12631 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第603行运算过程: if ((_12298 & 16777216u) == 16777216u)
  无运算过程
==================================================
第605行运算过程: float _12641 = _12631 / ((_12602 * _Block1.LightDataBuffer[_12202].w) + 9.9999997473787516355514526367188e-05);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = _12602 * tmp_0
    结果类型: float
    操作数类型: float, float4

  步骤3: tmp_2 = _12631 / tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _12641 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第607行运算过程: if (_12858 > 0.00999999977648258209228515625)
  无运算过程
==================================================
第609行运算过程: _19211 = fast::min(_12641, _12858);
运算过程详情:
  步骤1: tmp_0 = fast::min(_12641, _12858)
    结果类型: float
    操作数类型: float, float

  步骤2: _19211 = tmp_0
    操作数类型: float

==================================================
第613行运算过程: _19211 = _12641;
运算过程详情:
  步骤1: _19211 = _12641
    操作数类型: float

==================================================
第615行运算过程: _19213 = fast::min(100.0, _19211);
运算过程详情:
  步骤1: tmp_0 = fast::min(100.0, _19211)
    结果类型: float
    操作数类型: float, unknown

  步骤2: _19213 = tmp_0
    操作数类型: float

==================================================
第619行运算过程: _19213 = _12631 * 0.100000001490116119384765625;
运算过程详情:
  步骤1: tmp_0 = _12631 * 0.100000001490116119384765625
    结果类型: float
    操作数类型: float, float

  步骤2: _19213 = tmp_0
    操作数类型: float

==================================================
第621行运算过程: float _12677 = fast::clamp((dot(_Block1.LightDataBuffer[_12202].xyz, -_12606) - _Block1.LightDataBuffer[_12209].z) * _Block1.LightDataBuffer[_12209].y, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = dot(tmp_0)
    结果类型: float
    操作数类型: float4

  步骤3: tmp_2 = fast::clamp(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: _12677 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第622行运算过程: _19822 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19213));
运算过程详情:
  步骤1: tmp_0 = float3(3000.0)
    结果类型: float3
    操作数类型: float

  步骤2: tmp_1 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = fast::min(tmp_0, tmp_1)
    结果类型: float4
    操作数类型: float3, float4

  步骤4: tmp_3 = half3(tmp_2)
    结果类型: half3
    操作数类型: float4

  步骤5: _19822 = tmp_3
    操作数类型: half3

==================================================
第623行运算过程: _19787 = _12677 * _12677;
运算过程详情:
  步骤1: tmp_0 = _12677 * _12677
    结果类型: float
    操作数类型: float, float

  步骤2: _19787 = tmp_0
    操作数类型: float

==================================================
第624行运算过程: _19752 = _12606;
运算过程详情:
  步骤1: _19752 = _12606
    操作数类型: float3

==================================================
第625行运算过程: _19717 = _11517;
运算过程详情:
  步骤1: _19717 = _11517
    操作数类型: float3

==================================================
第626行运算过程: _19682 = _9109;
运算过程详情:
  步骤1: _19682 = _9109
    操作数类型: float3

==================================================
第627行运算过程: _19610 = 0;
运算过程详情:
  步骤1: _19610 = 0
    操作数类型: int

==================================================
第628行运算过程: _19574 = float((_12741 >> 16u) & 65535u) * 0.001525902189314365386962890625;
运算过程详情:
  步骤1: tmp_0 = float(_12741)
    结果类型: float
    操作数类型: uint

  步骤2: _19574 = tmp_0
    操作数类型: float

==================================================
第629行运算过程: _19482 = clamp(half(dot(_9109, _12606)), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _12606)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = clamp(tmp_1, tmp_2, tmp_3)
    结果类型: half
    操作数类型: half, half, half

  步骤6: _19482 = tmp_4
    操作数类型: half

==================================================
第641行运算过程: if (_12309 == 65536u)
  无运算过程
==================================================
第643行运算过程: uint _13098 = as_type<uint>(_Block1.LightDataBuffer[_12195].w);
运算过程详情:
  步骤1: _13098 = as_type
    结果类型: uint
    操作数类型: unknown

==================================================
第644行运算过程: float3 _12933 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: _12933 = tmp_0
    结果类型: float3
    操作数类型: float4

==================================================
第645行运算过程: float _12936 = dot(_12933, _12933);
运算过程详情:
  步骤1: tmp_0 = dot(_12933, _12933)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _12936 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第646行运算过程: float3 _12942 = _12933 / float3(sqrt(_12936));
运算过程详情:
  步骤1: tmp_0 = sqrt(_12936)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float

  步骤3: tmp_2 = _12933 / tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _12942 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第647行运算过程: float _12950 = _12936 * abs(_Block1.LightDataBuffer[_12188].w);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = _12936 * tmp_1
    结果类型: float
    操作数类型: float, float4

  步骤4: _12950 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第648行运算过程: float _12956 = fast::clamp(1.0 - (_12950 * _12950), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _12950 * _12950
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: _12956 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第649行运算过程: float _12972 = fast::min(100.0, (_12956 * _12956) / ((_12936 * _Block1.LightDataBuffer[_12209].w) + 9.9999997473787516355514526367188e-05));
运算过程详情:
  步骤1: tmp_0 = _12956 * _12956
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = _12936 * tmp_1
    结果类型: float
    操作数类型: float, float4

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::min(100.0, tmp_3)
    结果类型: float
    操作数类型: float, float

  步骤6: _12972 = tmp_4
    结果类型: float
    操作数类型: float

==================================================
第650行运算过程: float _13202 = float((_13098 >> 0u) & 65535u) * 0.0001525902189314365386962890625;
运算过程详情:
  步骤1: tmp_0 = float(_13098)
    结果类型: float
    操作数类型: uint

  步骤2: _13202 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第652行运算过程: if (_13202 > 0.00999999977648258209228515625)
  无运算过程
==================================================
第654行运算过程: _19070 = fast::min(_12972, _13202);
运算过程详情:
  步骤1: tmp_0 = fast::min(_12972, _13202)
    结果类型: float
    操作数类型: float, float

  步骤2: _19070 = tmp_0
    操作数类型: float

==================================================
第658行运算过程: _19070 = _12972;
运算过程详情:
  步骤1: _19070 = _12972
    操作数类型: float

==================================================
第660行运算过程: _19823 = half3(fast::min(float3(3000.0), _Block1.LightDataBuffer[_12195].xyz * _19070) * (((_12298 & 16777216u) == 16777216u) ? _Block1.TimeOfDayInfos.y : 1.0));
  无运算过程
==================================================
第661行运算过程: _19788 = 1.0;
运算过程详情:
  步骤1: _19788 = 1.0
    操作数类型: float

==================================================
第662行运算过程: _19753 = _12942;
运算过程详情:
  步骤1: _19753 = _12942
    操作数类型: float3

==================================================
第663行运算过程: _19718 = _11517;
运算过程详情:
  步骤1: _19718 = _11517
    操作数类型: float3

==================================================
第664行运算过程: _19683 = _9109;
运算过程详情:
  步骤1: _19683 = _9109
    操作数类型: float3

==================================================
第665行运算过程: _19611 = 0;
运算过程详情:
  步骤1: _19611 = 0
    操作数类型: int

==================================================
第666行运算过程: _19575 = float((_13098 >> 16u) & 65535u) * 0.001525902189314365386962890625;
运算过程详情:
  步骤1: tmp_0 = float(_13098)
    结果类型: float
    操作数类型: uint

  步骤2: _19575 = tmp_0
    操作数类型: float

==================================================
第667行运算过程: _19483 = ((_12298 & 262144u) == 262144u) ? _9019 : half(fast::clamp(dot(_9109, _12942), 0.0, 1.0));
  无运算过程
==================================================
第671行运算过程: bool _13270 = _12309 == 131072u;
  无运算过程
==================================================
第677行运算过程: if (_13270)
  无运算过程
==================================================
第679行运算过程: float3 _13339 = _Block1.LightDataBuffer[_12188].xyz - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: _13339 = tmp_0
    结果类型: float3
    操作数类型: float4

==================================================
第680行运算过程: float _13342 = dot(_13339, _13339);
运算过程详情:
  步骤1: tmp_0 = dot(_13339, _13339)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _13342 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第681行运算过程: float _13348 = _13342 * abs(_Block1.LightDataBuffer[_12188].w);
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = _13342 * tmp_1
    结果类型: float
    操作数类型: float, float4

  步骤4: _13348 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第682行运算过程: float _13356 = fast::clamp(1.0 - (_13348 * _13348), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _13348 * _13348
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: _13356 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第683行运算过程: float3 _13433 = fast::normalize(_11517 - (_9109 * _11925));
运算过程详情:
  步骤1: tmp_0 = _9109 * _11925
    结果类型: float
    操作数类型: float3, float

  步骤2: tmp_1 = _11517 - tmp_0
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = fast::normalize(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: _13433 = tmp_2
    结果类型: float3
    操作数类型: float

==================================================
第684行运算过程: float3x3 _13459 = float3x3(_13433, cross(_9109, _13433), _9109);
运算过程详情:
  步骤1: tmp_0 = cross(_9109, _13433)
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = float3x3(_13433, tmp_0, _9109)
    结果类型: float3x3
    操作数类型: float3, float3, float3

  步骤3: _13459 = tmp_1
    结果类型: float3x3
    操作数类型: float3x3

==================================================
第685行运算过程: float3 _13466 = float3(_Block1.LightDataBuffer[_12202].xyz) * _Block1.LightDataBuffer[_12195].w;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float4

  步骤3: _13466 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第686行运算过程: float3 _13467 = _13339 - _13466;
运算过程详情:
  步骤1: tmp_0 = _13339 - _13466
    结果类型: float3
    操作数类型: float3, float3

  步骤2: _13467 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第687行运算过程: float3 _13472 = float3(_Block1.LightDataBuffer[_12209].yzw) * _Block1.LightDataBuffer[_12202].w;
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: float4

  步骤3: _13472 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第688行运算过程: float3 _13484 = _13339 + _13466;
运算过程详情:
  步骤1: tmp_0 = _13339 + _13466
    结果类型: float3
    操作数类型: float3, float3

  步骤2: _13484 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第689行运算过程: float3 _13657 = fast::normalize((_13467 - _13472) * _13459);
运算过程详情:
  步骤1: tmp_0 = _13467 - _13472
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = tmp_0 * _13459
    结果类型: float3
    操作数类型: float3, float3x3

  步骤3: tmp_2 = fast::normalize(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: _13657 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第690行运算过程: float3 _13660 = fast::normalize((_13484 - _13472) * _13459);
运算过程详情:
  步骤1: tmp_0 = _13484 - _13472
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = tmp_0 * _13459
    结果类型: float3
    操作数类型: float3, float3x3

  步骤3: tmp_2 = fast::normalize(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: _13660 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第691行运算过程: float3 _13663 = fast::normalize((_13484 + _13472) * _13459);
运算过程详情:
  步骤1: tmp_0 = _13484 + _13472
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = tmp_0 * _13459
    结果类型: float3
    操作数类型: float3, float3x3

  步骤3: tmp_2 = fast::normalize(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: _13663 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第692行运算过程: float3 _13666 = fast::normalize((_13467 + _13472) * _13459);
运算过程详情:
  步骤1: tmp_0 = _13467 + _13472
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = tmp_0 * _13459
    结果类型: float3
    操作数类型: float3, float3x3

  步骤3: tmp_2 = fast::normalize(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: _13666 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第693行运算过程: float _13712 = dot(_13657, _13660);
运算过程详情:
  步骤1: tmp_0 = dot(_13657, _13660)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _13712 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第694行运算过程: float _13714 = abs(_13712);
运算过程详情:
  步骤1: tmp_0 = abs(_13712)
    结果类型: float
    操作数类型: float

  步骤2: _13714 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第695行运算过程: float _13728 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13714)) * _13714)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13714) * _13714));
运算过程详情:
  步骤1: tmp_0 = 0.01452060043811798095703125 * _13714
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 0.4965155124664306640625 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 * _13714
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = 0.8543984889984130859375 + tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = 4.1616725921630859375 + _13714
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_4 * _13714
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = 3.41759395599365234375 + tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_3 / tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: _13728 = tmp_7
    结果类型: float
    操作数类型: float

==================================================
第696行运算过程: float _13753 = dot(_13660, _13663);
运算过程详情:
  步骤1: tmp_0 = dot(_13660, _13663)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _13753 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第697行运算过程: float _13755 = abs(_13753);
运算过程详情:
  步骤1: tmp_0 = abs(_13753)
    结果类型: float
    操作数类型: float

  步骤2: _13755 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第698行运算过程: float _13769 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13755)) * _13755)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13755) * _13755));
运算过程详情:
  步骤1: tmp_0 = 0.01452060043811798095703125 * _13755
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 0.4965155124664306640625 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 * _13755
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = 0.8543984889984130859375 + tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = 4.1616725921630859375 + _13755
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_4 * _13755
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = 3.41759395599365234375 + tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_3 / tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: _13769 = tmp_7
    结果类型: float
    操作数类型: float

==================================================
第699行运算过程: float _13794 = dot(_13663, _13666);
运算过程详情:
  步骤1: tmp_0 = dot(_13663, _13666)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _13794 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第700行运算过程: float _13796 = abs(_13794);
运算过程详情:
  步骤1: tmp_0 = abs(_13794)
    结果类型: float
    操作数类型: float

  步骤2: _13796 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第701行运算过程: float _13810 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13796)) * _13796)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13796) * _13796));
运算过程详情:
  步骤1: tmp_0 = 0.01452060043811798095703125 * _13796
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 0.4965155124664306640625 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 * _13796
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = 0.8543984889984130859375 + tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = 4.1616725921630859375 + _13796
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_4 * _13796
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = 3.41759395599365234375 + tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_3 / tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: _13810 = tmp_7
    结果类型: float
    操作数类型: float

==================================================
第702行运算过程: float _13835 = dot(_13666, _13657);
运算过程详情:
  步骤1: tmp_0 = dot(_13666, _13657)
    结果类型: float
    操作数类型: float3, float3

  步骤2: _13835 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第703行运算过程: float _13837 = abs(_13835);
运算过程详情:
  步骤1: tmp_0 = abs(_13835)
    结果类型: float
    操作数类型: float

  步骤2: _13837 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第704行运算过程: float _13851 = (0.8543984889984130859375 + ((0.4965155124664306640625 + (0.01452060043811798095703125 * _13837)) * _13837)) / (3.41759395599365234375 + ((4.1616725921630859375 + _13837) * _13837));
运算过程详情:
  步骤1: tmp_0 = 0.01452060043811798095703125 * _13837
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 0.4965155124664306640625 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 * _13837
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = 0.8543984889984130859375 + tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = 4.1616725921630859375 + _13837
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_4 * _13837
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = 3.41759395599365234375 + tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_3 / tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: _13851 = tmp_7
    结果类型: float
    操作数类型: float

==================================================
第705行运算过程: float3 _13700 = cross(_13660, (_13657 * (-((_13712 > 0.0) ? _13728 : ((0.5 * rsqrt(fast::max(1.0 - (_13712 * _13712), 1.0000000116860974230803549289703e-07))) - _13728)))) + (_13663 * ((_13753 > 0.0) ? _13769 : ((0.5 * rsqrt(fast::max(1.0 - (_13753 * _13753), 1.0000000116860974230803549289703e-07))) - _13769)))) + cross(_13666, (_13657 * ((_13835 > 0.0) ? _13851 : ((0.5 * rsqrt(fast::max(1.0 - (_13835 * _13835), 1.0000000116860974230803549289703e-07))) - _13851))) + (_13663 * (-((_13794 > 0.0) ? _13810 : ((0.5 * rsqrt(fast::max(1.0 - (_13794 * _13794), 1.0000000116860974230803549289703e-07))) - _13810)))));
运算过程详情:
  步骤1: tmp_0 = -_13712
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _13657 * tmp_0
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = cross(_13660, tmp_1)
    结果类型: float3
    操作数类型: float3, float

  步骤4: _13700 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第706行运算过程: float _13531 = length(_13700);
运算过程详情:
  步骤1: tmp_0 = length(_13700)
    结果类型: float
    操作数类型: float3

  步骤2: _13531 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第707行运算过程: float _13539 = step(0.0, dot(cross(_Block1.LightDataBuffer[_12202].xyz, _Block1.LightDataBuffer[_12209].yzw), _13339));
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = cross(tmp_0)
    结果类型: float3
    操作数类型: float4

  步骤3: tmp_2 = dot(tmp_1)
    结果类型: float
    操作数类型: float3

  步骤4: tmp_3 = step(0.0, tmp_2)
    结果类型: float
    操作数类型: float, float

  步骤5: _13539 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第708行运算过程: _19824 = half3(_Block1.LightDataBuffer[_12195].xyz * (_13356 * _13356));
运算过程详情:
  步骤1: tmp_0 = _Block1.LightDataBuffer
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float4

  步骤3: _19824 = tmp_1
    操作数类型: half3

==================================================
第709行运算过程: _19789 = ((!((_12298 & 67108864u) == 67108864u)) && (_13539 > 0.0)) ? 0.0 : _13531;
  无运算过程
==================================================
第710行运算过程: _19754 = _13339 * rsqrt(_13342);
运算过程详情:
  步骤1: tmp_0 = rsqrt(_13342)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _13339 * tmp_0
    结果类型: float
    操作数类型: float3, float

  步骤3: _19754 = tmp_1
    操作数类型: float

==================================================
第711行运算过程: _19684 = _9109;
运算过程详情:
  步骤1: _19684 = _9109
    操作数类型: float3

==================================================
第712行运算过程: _19484 = half(fast::max(((_13531 * _13531) + ((_13700 / float3(_13531)).z * ((_13539 * 2.0) - 1.0))) / (_13531 + 1.0), 0.0));
运算过程详情:
  步骤1: tmp_0 = _13531 * _13531
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = float3(_13531)
    结果类型: float3
    操作数类型: float

  步骤3: tmp_2 = _13700 / tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: tmp_3 = tmp_0 + tmp_2
    结果类型: float
    操作数类型: float, float3

  步骤5: tmp_4 = fast::max(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = half(tmp_4)
    结果类型: half
    操作数类型: float

  步骤7: _19484 = tmp_5
    操作数类型: half

==================================================
第716行运算过程: _19824 = _19825;
运算过程详情:
  步骤1: _19824 = _19825
    操作数类型: unknown

==================================================
第717行运算过程: _19789 = _19790;
运算过程详情:
  步骤1: _19789 = _19790
    操作数类型: unknown

==================================================
第718行运算过程: _19754 = _19755;
运算过程详情:
  步骤1: _19754 = _19755
    操作数类型: unknown

==================================================
第719行运算过程: _19684 = _19685;
运算过程详情:
  步骤1: _19684 = _19685
    操作数类型: unknown

==================================================
第720行运算过程: _19484 = _19485;
运算过程详情:
  步骤1: _19484 = _19485
    操作数类型: unknown

==================================================
第722行运算过程: _19823 = _19824;
运算过程详情:
  步骤1: _19823 = _19824
    操作数类型: unknown

==================================================
第723行运算过程: _19788 = _19789;
运算过程详情:
  步骤1: _19788 = _19789
    操作数类型: unknown

==================================================
第724行运算过程: _19753 = _19754;
运算过程详情:
  步骤1: _19753 = _19754
    操作数类型: unknown

==================================================
第725行运算过程: _19718 = select(_19720, _11517, bool3(_13270));
运算过程详情:
  步骤1: tmp_0 = bool3(_13270)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = select(_19720, _11517, tmp_0)
    结果类型: float
    操作数类型: unknown, float3, float

  步骤3: _19718 = tmp_1
    操作数类型: float

==================================================
第726行运算过程: _19683 = _19684;
运算过程详情:
  步骤1: _19683 = _19684
    操作数类型: unknown

==================================================
第727行运算过程: _19611 = _13270 ? 0 : _19613;
运算过程详情:
  步骤1: _19611 = _13270
    操作数类型: unknown

==================================================
第728行运算过程: _19575 = _13270 ? 1.0 : _19577;
运算过程详情:
  步骤1: _19575 = _13270
    操作数类型: unknown

==================================================
第729行运算过程: _19483 = _19484;
运算过程详情:
  步骤1: _19483 = _19484
    操作数类型: unknown

==================================================
第731行运算过程: _19822 = _19823;
运算过程详情:
  步骤1: _19822 = _19823
    操作数类型: unknown

==================================================
第732行运算过程: _19787 = _19788;
运算过程详情:
  步骤1: _19787 = _19788
    操作数类型: unknown

==================================================
第733行运算过程: _19752 = _19753;
运算过程详情:
  步骤1: _19752 = _19753
    操作数类型: unknown

==================================================
第734行运算过程: _19717 = _19718;
运算过程详情:
  步骤1: _19717 = _19718
    操作数类型: unknown

==================================================
第735行运算过程: _19682 = _19683;
运算过程详情:
  步骤1: _19682 = _19683
    操作数类型: unknown

==================================================
第736行运算过程: _19610 = _19611;
运算过程详情:
  步骤1: _19610 = _19611
    操作数类型: unknown

==================================================
第737行运算过程: _19574 = _19575;
运算过程详情:
  步骤1: _19574 = _19575
    操作数类型: unknown

==================================================
第738行运算过程: _19482 = _19483;
运算过程详情:
  步骤1: _19482 = _19483
    操作数类型: unknown

==================================================
第740行运算过程: _19821 = _19822;
运算过程详情:
  步骤1: _19821 = _19822
    操作数类型: unknown

==================================================
第741行运算过程: _19786 = _19787;
运算过程详情:
  步骤1: _19786 = _19787
    操作数类型: unknown

==================================================
第742行运算过程: _19751 = _19752;
运算过程详情:
  步骤1: _19751 = _19752
    操作数类型: unknown

==================================================
第743行运算过程: _19716 = _19717;
运算过程详情:
  步骤1: _19716 = _19717
    操作数类型: unknown

==================================================
第744行运算过程: _19681 = _19682;
运算过程详情:
  步骤1: _19681 = _19682
    操作数类型: unknown

==================================================
第745行运算过程: _19609 = _19610;
运算过程详情:
  步骤1: _19609 = _19610
    操作数类型: unknown

==================================================
第746行运算过程: _19573 = _19574;
运算过程详情:
  步骤1: _19573 = _19574
    操作数类型: unknown

==================================================
第747行运算过程: _19481 = _19482;
运算过程详情:
  步骤1: _19481 = _19482
    操作数类型: unknown

==================================================
第749行运算过程: float _14176 = float(_19481);
运算过程详情:
  步骤1: tmp_0 = float(_19481)
    结果类型: float
    操作数类型: unknown

  步骤2: _14176 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第750行运算过程: float3 _14197 = fast::normalize(_19716 + _19751);
运算过程详情:
  步骤1: tmp_0 = _19716 + _19751
    结果类型: unknown
    操作数类型: unknown, unknown

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: unknown
    操作数类型: unknown

  步骤3: _14197 = tmp_1
    结果类型: float3
    操作数类型: unknown

==================================================
第751行运算过程: float _14202 = fast::clamp(dot(_19681, _14197), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = dot(_19681, _14197)
    结果类型: float
    操作数类型: unknown, float3

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: _14202 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第752行运算过程: float _14208 = fast::clamp(dot(_19716, _14197), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = dot(_19716, _14197)
    结果类型: float
    操作数类型: unknown, float3

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: _14208 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第753行运算过程: float _13993 = fast::clamp(abs(fast::clamp(dot(_19681, _19716), 0.0, 1.0)) + 9.9999997473787516355514526367188e-06, 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = dot(_19681, _19716)
    结果类型: float
    操作数类型: unknown, unknown

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = abs(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = tmp_2 + 9.9999997473787516355514526367188
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::clamp(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: _13993 = tmp_4
    结果类型: float
    操作数类型: float

==================================================
第754行运算过程: float _14013 = exp2((((-5.554729938507080078125) * _14208) - 6.9831600189208984375) * _14208);
运算过程详情:
  步骤1: tmp_0 = -5.554729938507080078125
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * _14208
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 6.9831600189208984375
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _14208
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = exp2(tmp_3)
    结果类型: float
    操作数类型: float

  步骤6: _14013 = tmp_4
    结果类型: float
    操作数类型: float

==================================================
第755行运算过程: float _14020 = _14013 + ((1.0 - _14013) * 0.039999999105930328369140625);
运算过程详情:
  步骤1: tmp_0 = 1.0 - _14013
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * 0.039999999105930328369140625
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = _14013 + tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: _14020 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第756行运算过程: half _14029 = _9199 - (_9199 - _19481);
运算过程详情:
  步骤1: tmp_0 = _9199 - _19481
    结果类型: half
    操作数类型: half, unknown

  步骤2: tmp_1 = _9199 - tmp_0
    结果类型: half
    操作数类型: half, half

  步骤3: _14029 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第757行运算过程: half3 _14069 = half3(((float3(_19821) * float3(_19786)) * _14176) * float(clamp(((_14029 + _11768) / _11777) * _11777, half(0.0), half(1.0)) * clamp(((_19481 + _11764) / _11790) * _11790, half(0.0), half(1.0))));
运算过程详情:
  步骤1: tmp_0 = float3(_19821)
    结果类型: float3
    操作数类型: unknown

  步骤2: tmp_1 = float3(_19786)
    结果类型: float3
    操作数类型: unknown

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: tmp_3 = tmp_2 * _14176
    结果类型: float
    操作数类型: float3, float

  步骤5: tmp_4 = _14029 + _11768
    结果类型: half
    操作数类型: half, half

  步骤6: tmp_5 = tmp_4 / _11777
    结果类型: half
    操作数类型: half, half

  步骤7: tmp_6 = tmp_5 * _11777
    结果类型: half
    操作数类型: half, half

  步骤8: tmp_7 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤9: tmp_8 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤10: tmp_9 = clamp(tmp_6, tmp_7, tmp_8)
    结果类型: half
    操作数类型: half, half, half

  步骤11: tmp_10 = _19481 + _11764
    结果类型: half
    操作数类型: unknown, half

  步骤12: tmp_11 = tmp_10 / _11790
    结果类型: half
    操作数类型: half, half

  步骤13: tmp_12 = tmp_11 * _11790
    结果类型: half
    操作数类型: half, half

  步骤14: tmp_13 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤15: tmp_14 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤16: tmp_15 = clamp(tmp_12, tmp_13, tmp_14)
    结果类型: half
    操作数类型: half, half, half

  步骤17: tmp_16 = tmp_9 * tmp_15
    结果类型: half
    操作数类型: half, half

  步骤18: tmp_17 = float(tmp_16)
    结果类型: float
    操作数类型: half

  步骤19: tmp_18 = tmp_3 * tmp_17
    结果类型: float
    操作数类型: float, float

  步骤20: tmp_19 = half3(tmp_18)
    结果类型: half3
    操作数类型: float

  步骤21: _14069 = tmp_19
    结果类型: half3
    操作数类型: half3

==================================================
第758行运算过程: half _14084 = clamp(dot(half3(fast::normalize(_19751 + _19716)), _9064), half(0.0), half(1.0));
运算过程详情:
  步骤1: tmp_0 = _19751 + _19716
    结果类型: unknown
    操作数类型: unknown, unknown

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: unknown
    操作数类型: unknown

  步骤3: tmp_2 = half3(tmp_1)
    结果类型: half3
    操作数类型: unknown

  步骤4: tmp_3 = dot(tmp_2, _9064)
    结果类型: float
    操作数类型: half3, half3

  步骤5: tmp_4 = half(0.0)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤7: tmp_6 = clamp(tmp_3, tmp_4, tmp_5)
    结果类型: float
    操作数类型: float, half, half

  步骤8: _14084 = tmp_6
    结果类型: half
    操作数类型: float

==================================================
第762行运算过程: if (_19609 >= 1)
  无运算过程
==================================================
第764行运算过程: float _14319 = _12049 / (((((_14202 * _12049) * _12049) - _14202) * _14202) + 1.0);
运算过程详情:
  步骤1: tmp_0 = _14202 * _12049
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * _12049
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - _14202
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _14202
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_3 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _12049 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _14319 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第765行运算过程: _19883 = (_12025 + (_12120 * _14013)) * (fast::min(1000.0, (_14319 * _14319) * 0.3183098733425140380859375) * (1.0 / fast::max((_13993 + sqrt((_13993 * (_13993 - (_13993 * _12080))) + _12080)) * (_14176 + sqrt((_14176 * (_14176 - (_14176 * _12080))) + _12080)), _12108)));
运算过程详情:
  步骤1: tmp_0 = _12120 * _14013
    结果类型: float
    操作数类型: float3, float

  步骤2: tmp_1 = _12025 + tmp_0
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = _14319 * _14319
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.3183098733425140380859375
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::min(1000.0, tmp_3)
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _13993 * _12080
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = _13993 - tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = _13993 * tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = tmp_7 + _12080
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = sqrt(tmp_8)
    结果类型: float
    操作数类型: float

  步骤11: tmp_10 = _13993 + tmp_9
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = _14176 * _12080
    结果类型: float
    操作数类型: float, float

  步骤13: tmp_12 = _14176 - tmp_11
    结果类型: float
    操作数类型: float, float

  步骤14: tmp_13 = _14176 * tmp_12
    结果类型: float
    操作数类型: float, float

  步骤15: tmp_14 = tmp_13 + _12080
    结果类型: float
    操作数类型: float, float

  步骤16: tmp_15 = sqrt(tmp_14)
    结果类型: float
    操作数类型: float

  步骤17: tmp_16 = _14176 + tmp_15
    结果类型: float
    操作数类型: float, float

  步骤18: tmp_17 = tmp_10 * tmp_16
    结果类型: float
    操作数类型: float, float

  步骤19: tmp_18 = fast::max(tmp_17, _12108)
    结果类型: float
    操作数类型: float, float

  步骤20: tmp_19 = 1.0 / tmp_18
    结果类型: float
    操作数类型: float, float

  步骤21: tmp_20 = tmp_4 * tmp_19
    结果类型: float
    操作数类型: float, float

  步骤22: tmp_21 = tmp_1 * tmp_20
    结果类型: float
    操作数类型: float, float

  步骤23: _19883 = tmp_21
    操作数类型: float

==================================================
第770行运算过程: _19883 = float3(0.0);
运算过程详情:
  步骤1: tmp_0 = float3(0.0)
    结果类型: float3
    操作数类型: float

  步骤2: _19883 = tmp_0
    操作数类型: float3

==================================================
第773行运算过程: break; // unreachable workaround
  无运算过程
==================================================
第774行运算过程: } while(false);
  无运算过程
==================================================
第775行运算过程: _20953 = _19821;
运算过程详情:
  步骤1: _20953 = _19821
    操作数类型: unknown

==================================================
第776行运算过程: _20938 = _19786;
运算过程详情:
  步骤1: _20938 = _19786
    操作数类型: unknown

==================================================
第777行运算过程: _20923 = _19751;
运算过程详情:
  步骤1: _20923 = _19751
    操作数类型: unknown

==================================================
第778行运算过程: _20908 = _19716;
运算过程详情:
  步骤1: _20908 = _19716
    操作数类型: unknown

==================================================
第779行运算过程: _20893 = _19681;
运算过程详情:
  步骤1: _20893 = _19681
    操作数类型: unknown

==================================================
第780行运算过程: _20863 = _19609;
运算过程详情:
  步骤1: _20863 = _19609
    操作数类型: unknown

==================================================
第781行运算过程: _20848 = _19573;
运算过程详情:
  步骤1: _20848 = _19573
    操作数类型: unknown

==================================================
第782行运算过程: _20833 = _19481;
运算过程详情:
  步骤1: _20833 = _19481
    操作数类型: unknown

==================================================
第783行运算过程: _19965 = (_18431 + (float3(mix(_9179, half3(half(((_11862 * powr(float(half(fast::max(float(_9199 - (_14084 * _14084)), 0.0078125))), _11858)) * 0.15915493667125701904296875) * float(half4(float4(_11970 / powr(max(half4(half(9.9956989288330078125e-05)), _11973 - (_11975 * (-half(dot(_19716, _19751))))), _11982)) * float4(0.0795769989490509033203125)).x))), half3(half(fast::clamp((_14020 * _14020) * float(dot(_14069, _8303)), 0.0, 1.0)))) * _14069) * float3(_19573))) + (((float3(_14069) * _19883) * _19573) * float(_14029));
运算过程详情:
  步骤1: tmp_0 = _14084 * _14084
    结果类型: half
    操作数类型: half, half

  步骤2: tmp_1 = _9199 - tmp_0
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: tmp_3 = fast::max(tmp_2, 0.0078125)
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = float(tmp_4)
    结果类型: float
    操作数类型: half

  步骤7: tmp_6 = powr(tmp_5, _11858)
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = _11862 * tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = tmp_7 * 0.15915493667125701904296875
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤11: tmp_10 = half4(tmp_9)
    结果类型: half4
    操作数类型: half

  步骤12: tmp_11 = max(tmp_10)
    结果类型: float
    操作数类型: half4

  步骤13: tmp_12 = powr(tmp_11)
    结果类型: float
    操作数类型: float

  步骤14: tmp_13 = _11970 / tmp_12
    结果类型: float
    操作数类型: half4, float

  步骤15: tmp_14 = float4(tmp_13)
    结果类型: float4
    操作数类型: float

  步骤16: tmp_15 = half4(tmp_14)
    结果类型: half4
    操作数类型: float4

  步骤17: tmp_16 = float(tmp_15)
    结果类型: float
    操作数类型: half4

  步骤18: tmp_17 = tmp_8 * tmp_16
    结果类型: float
    操作数类型: float, float

  步骤19: tmp_18 = half(tmp_17)
    结果类型: half
    操作数类型: float

  步骤20: tmp_19 = half3(tmp_18)
    结果类型: half3
    操作数类型: half

  步骤21: tmp_20 = mix(_9179, tmp_19)
    结果类型: half3
    操作数类型: half3, half3

  步骤22: tmp_21 = float3(tmp_20)
    结果类型: float3
    操作数类型: half3

  步骤23: tmp_22 = _18431 + tmp_21
    结果类型: float3
    操作数类型: unknown, float3

  步骤24: _19965 = tmp_22
    操作数类型: float3

==================================================
第784行运算过程: _19923 = _18429 + (_14069 * _14029);
运算过程详情:
  步骤1: tmp_0 = _14069 * _14029
    结果类型: half
    操作数类型: half3, half

  步骤2: tmp_1 = _18429 + tmp_0
    结果类型: half
    操作数类型: unknown, half

  步骤3: _19923 = tmp_1
    操作数类型: half

==================================================
第786行运算过程: half3 _8560 = (_9179 + (((_9179 + (_11812 * _11772)) * _9254) * half3(half(_Block1.EnvInfo.z)))) + _18429;
运算过程详情:
  步骤1: tmp_0 = _11812 * _11772
    结果类型: half
    操作数类型: half3, half

  步骤2: tmp_1 = _9179 + tmp_0
    结果类型: half
    操作数类型: half3, half

  步骤3: tmp_2 = tmp_1 * _9254
    结果类型: half
    操作数类型: half, half3

  步骤4: tmp_3 = _Block1.EnvInfo.z
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = half3(tmp_4)
    结果类型: half3
    操作数类型: half

  步骤7: tmp_6 = tmp_2 * tmp_5
    结果类型: half
    操作数类型: half, half3

  步骤8: tmp_7 = _9179 + tmp_6
    结果类型: half
    操作数类型: half3, half

  步骤9: tmp_8 = tmp_7 + _18429
    结果类型: half
    操作数类型: half, unknown

  步骤10: _8560 = tmp_8
    结果类型: half3
    操作数类型: half

==================================================
第787行运算过程: float3 _8565 = (_9698 + _8521) + _18431;
运算过程详情:
  步骤1: tmp_0 = _9698 + _8521
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = tmp_0 + _18431
    结果类型: float3
    操作数类型: float3, unknown

  步骤3: _8565 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第788行运算过程: bool _8573 = (_8397 & 16128u) > 0u;
运算过程详情:
  步骤1: _8573 = _8397
    结果类型: bool
    操作数类型: uint

==================================================
第791行运算过程: if (_8573)
  无运算过程
==================================================
第793行运算过程: bool _8590 = (_8397 & 16384u) > 0u;
运算过程详情:
  步骤1: _8590 = _8397
    结果类型: bool
    操作数类型: uint

==================================================
第796行运算过程: if (_8573)
  无运算过程
==================================================
第798行运算过程: uint _14451 = (_8397 & 458752u) >> 16u;
运算过程详情:
  步骤1: _14451 = _8397
    结果类型: uint
    操作数类型: uint

==================================================
第799行运算过程: uint _14469 = (_8397 & 4026531840u) >> 28u;
运算过程详情:
  步骤1: _14469 = _8397
    结果类型: uint
    操作数类型: uint

==================================================
第800行运算过程: float _14482 = fast::clamp((_Block1.CameraInfo.y * in.IN_LinearZ) / fast::max(_Block1.SHGIParam2.x, 0.001000000047497451305389404296875), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraInfo.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = in.IN_LinearZ
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = _Block1.SHGIParam2.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = fast::max(tmp_3, 0.001000000047497451305389404296875)
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = tmp_2 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤8: _14482 = tmp_6
    结果类型: float
    操作数类型: float

==================================================
第803行运算过程: if ((_8397 & 2048u) != 0u)
  无运算过程
==================================================
第805行运算过程: float3 _14684 = select(_Block1.PlayerPos.xyz, _Block1.CameraPos.xyz, bool3((_8397 & 524288u) > 0u)) * float3(0.125);
运算过程详情:
  步骤1: tmp_0 = _Block1.PlayerPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.CameraPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = bool3(_8397)
    结果类型: float
    操作数类型: uint

  步骤4: tmp_3 = select(tmp_0, tmp_1, tmp_2)
    结果类型: float
    操作数类型: float3, float3, float

  步骤5: _14684 = tmp_3
    结果类型: float3
    操作数类型: float

==================================================
第806行运算过程: float _14692 = _14684.x;
运算过程详情:
  步骤1: tmp_0 = _14684.x
    结果类型: float
    操作数类型: float

  步骤2: _14692 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第807行运算过程: float _14694 = floor(_14692);
运算过程详情:
  步骤1: tmp_0 = floor(_14692)
    结果类型: float
    操作数类型: float

  步骤2: _14694 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第809行运算过程: _21212.x = _14694 - 15.0;
运算过程详情:
  步骤1: tmp_0 = _14694 - 15.0
    结果类型: float
    操作数类型: float, float

  步骤2: _21212.x = tmp_0
    操作数类型: float

==================================================
第811行运算过程: if ((_14692 - _14694) > 0.5)
  无运算过程
==================================================
第813行运算过程: float3 _21215 = _21212;
运算过程详情:
  步骤1: _21215 = _21212
    结果类型: float3
    操作数类型: unknown

==================================================
第814行运算过程: _21215.x = _14694 + (-14.0);
运算过程详情:
  步骤1: tmp_0 = -14.0
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _14694 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _21215.x = tmp_1
    操作数类型: float

==================================================
第815行运算过程: _21268 = _21215;
运算过程详情:
  步骤1: _21268 = _21215
    操作数类型: float3

==================================================
第819行运算过程: _21268 = _21212;
运算过程详情:
  步骤1: _21268 = _21212
    操作数类型: unknown

==================================================
第821行运算过程: float _21034 = _14684.y;
运算过程详情:
  步骤1: tmp_0 = _14684.y
    结果类型: float
    操作数类型: float

  步骤2: _21034 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第822行运算过程: float _21035 = floor(_21034);
运算过程详情:
  步骤1: tmp_0 = floor(_21034)
    结果类型: float
    操作数类型: float

  步骤2: _21035 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第823行运算过程: float3 _21219 = _21268;
运算过程详情:
  步骤1: _21219 = _21268
    结果类型: float3
    操作数类型: unknown

==================================================
第824行运算过程: _21219.y = _21035 - 8.0;
运算过程详情:
  步骤1: tmp_0 = _21035 - 8.0
    结果类型: float
    操作数类型: float, float

  步骤2: _21219.y = tmp_0
    操作数类型: float

==================================================
第826行运算过程: if ((_21034 - _21035) > 0.5)
  无运算过程
==================================================
第828行运算过程: float3 _21222 = _21219;
运算过程详情:
  步骤1: _21222 = _21219
    结果类型: float3
    操作数类型: float3

==================================================
第829行运算过程: _21222.y = _21035 + (-7.0);
运算过程详情:
  步骤1: tmp_0 = -7.0
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _21035 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _21222.y = tmp_1
    操作数类型: float

==================================================
第830行运算过程: _21269 = _21222;
运算过程详情:
  步骤1: _21269 = _21222
    操作数类型: float3

==================================================
第834行运算过程: _21269 = _21219;
运算过程详情:
  步骤1: _21269 = _21219
    操作数类型: float3

==================================================
第836行运算过程: float _21056 = _14684.z;
运算过程详情:
  步骤1: tmp_0 = _14684.z
    结果类型: float
    操作数类型: float

  步骤2: _21056 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第837行运算过程: float _21057 = floor(_21056);
运算过程详情:
  步骤1: tmp_0 = floor(_21056)
    结果类型: float
    操作数类型: float

  步骤2: _21057 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第838行运算过程: float3 _21226 = _21269;
运算过程详情:
  步骤1: _21226 = _21269
    结果类型: float3
    操作数类型: unknown

==================================================
第839行运算过程: _21226.z = _21057 - 15.0;
运算过程详情:
  步骤1: tmp_0 = _21057 - 15.0
    结果类型: float
    操作数类型: float, float

  步骤2: _21226.z = tmp_0
    操作数类型: float

==================================================
第841行运算过程: if ((_21056 - _21057) > 0.5)
  无运算过程
==================================================
第843行运算过程: float3 _21229 = _21226;
运算过程详情:
  步骤1: _21229 = _21226
    结果类型: float3
    操作数类型: float3

==================================================
第844行运算过程: _21229.z = _21057 + (-14.0);
运算过程详情:
  步骤1: tmp_0 = -14.0
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _21057 + tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _21229.z = tmp_1
    操作数类型: float

==================================================
第845行运算过程: _21270 = _21229;
运算过程详情:
  步骤1: _21270 = _21229
    操作数类型: float3

==================================================
第849行运算过程: _21270 = _21226;
运算过程详情:
  步骤1: _21270 = _21226
    操作数类型: float3

==================================================
第851行运算过程: float3 _14717 = _21270 * 8.0;
运算过程详情:
  步骤1: tmp_0 = _21270 * 8.0
    结果类型: float
    操作数类型: unknown, float

  步骤2: _14717 = tmp_0
    结果类型: float3
    操作数类型: float

==================================================
第854行运算过程: if (all(in.IN_WorldPosition.xyz >= _14717) && all(in.IN_WorldPosition.xyz < (_14717 + float3(240.0, 128.0, 240.0))))
  无运算过程
==================================================
第856行运算过程: float3 _14534 = (in.IN_WorldPosition.xyz + (_9109 * 0.100000001490116119384765625)) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _9109 * 0.100000001490116119384765625
    结果类型: float
    操作数类型: float3, float

  步骤3: tmp_2 = tmp_0 + tmp_1
    结果类型: float
    操作数类型: float3, float

  步骤4: tmp_3 = float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)
    结果类型: float3
    操作数类型: float, float, float

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float
    操作数类型: float, float3

  步骤6: _14534 = tmp_4
    结果类型: float3
    操作数类型: float

==================================================
第857行运算过程: float3 _14747 = _14534 - floor(_14534);
运算过程详情:
  步骤1: tmp_0 = floor(_14534)
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _14534 - tmp_0
    结果类型: float3
    操作数类型: float3, float3

  步骤3: _14747 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第858行运算过程: half3 _14556 = half3(_9109);
运算过程详情:
  步骤1: tmp_0 = half3(_9109)
    结果类型: half3
    操作数类型: float3

  步骤2: _14556 = tmp_0
    结果类型: half3
    操作数类型: half3

==================================================
第859行运算过程: float _14788 = float((_8397 & 15728640u) >> 20u);
运算过程详情:
  步骤1: tmp_0 = float(_8397)
    结果类型: float
    操作数类型: uint

  步骤2: _14788 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第860行运算过程: float _14797 = float(_14451);
运算过程详情:
  步骤1: tmp_0 = float(_14451)
    结果类型: float
    操作数类型: uint

  步骤2: _14797 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第861行运算过程: float _14827 = (_14788 - _14797) + ((3.0 - _14797) * 3.0);
运算过程详情:
  步骤1: tmp_0 = _14788 - _14797
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 3.0 - _14797
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 * 3.0
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_0 + tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: _14827 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第862行运算过程: float _14831 = _14827 + 1.0;
运算过程详情:
  步骤1: tmp_0 = _14827 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤2: _14831 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第863行运算过程: float _14833 = _14827 + 2.0;
运算过程详情:
  步骤1: tmp_0 = _14827 + 2.0
    结果类型: float
    操作数类型: float, float

  步骤2: _14833 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第866行运算过程: if (3 >= int(_14469))
  无运算过程
==================================================
第868行运算过程: float _14850 = (_14788 - float((_8397 & 251658240u) >> 24u)) + ((3.0 - float(_14469)) * 3.0);
运算过程详情:
  步骤1: tmp_0 = float(_8397)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = _14788 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _14850 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第869行运算过程: float _14854 = _14850 + 1.0;
运算过程详情:
  步骤1: tmp_0 = _14850 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤2: _14854 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第870行运算过程: float _14856 = _14850 + 2.0;
运算过程详情:
  步骤1: tmp_0 = _14850 + 2.0
    结果类型: float
    操作数类型: float, float

  步骤2: _14856 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第871行运算过程: float2 _14956 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _14747.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float2, float

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float
    操作数类型: float, float2

  步骤7: _14956 = tmp_5
    结果类型: float2
    操作数类型: float

==================================================
第872行运算过程: float _14963 = _14717.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _14963 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第873行运算过程: float _14967 = ((_14963 - floor(_14963)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_14963)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _14963 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _14967 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第874行运算过程: float _14973 = _14717.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _14973 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第875行运算过程: float _14977 = ((_14973 - floor(_14973)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_14973)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _14973 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _14977 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第876行运算过程: float _14982 = _14956.x;
运算过程详情:
  步骤1: tmp_0 = _14956.x
    结果类型: float
    操作数类型: float

  步骤2: _14982 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第878行运算过程: _18095.x = (_14982 < (_14967 + 0.5)) ? fast::min(_14982, _14967 + 0.49609375) : fast::max(_14982, _14967 + 0.50390625);
运算过程详情:
  步骤1: _18095.x = _14982
    操作数类型: float

==================================================
第879行运算过程: float _15000 = _14956.y;
运算过程详情:
  步骤1: tmp_0 = _14956.y
    结果类型: float
    操作数类型: float

  步骤2: _15000 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第880行运算过程: _18095.z = (_15000 < (_14977 + 0.5)) ? fast::min(_15000, _14977 + 0.49609375) : fast::max(_15000, _14977 + 0.50390625);
运算过程详情:
  步骤1: _18095.z = _15000
    操作数类型: float

==================================================
第881行运算过程: float _15021 = (_14747.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _14747.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: _15021 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第882行运算过程: float _15026 = floor(_15021);
运算过程详情:
  步骤1: tmp_0 = floor(_15021)
    结果类型: float
    操作数类型: float

  步骤2: _15026 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第883行运算过程: uint _15029 = (_15021 < 0.0) ? 63u : uint(_15026);
运算过程详情:
  步骤1: _15029 = _15021
    结果类型: uint
    操作数类型: float

==================================================
第884行运算过程: uint _15032 = _15029 + 1u;
运算过程详情:
  步骤1: tmp_0 = _15029 + 1
    结果类型: int
    操作数类型: uint, int

  步骤2: _15032 = tmp_0
    结果类型: uint
    操作数类型: int

==================================================
第885行运算过程: uint _21309 = (_15032 >= 64u) ? 0u : _15032;
  无运算过程
==================================================
第886行运算过程: float2 _15059 = (float2(float(_15029 & 7u), float(_15029 >> 3u)) + _18095.xz) * 0.125;
运算过程详情:
  步骤1: tmp_0 = float(_15029)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _15059 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第887行运算过程: float2 _15074 = (float2(float(_21309 & 7u), float(_21309 >> 3u)) + _18095.xz) * 0.125;
运算过程详情:
  步骤1: tmp_0 = float(_21309)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _15074 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第888行运算过程: float _15078 = _15059.x;
运算过程详情:
  步骤1: tmp_0 = _15059.x
    结果类型: float
    操作数类型: float

  步骤2: _15078 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第889行运算过程: float3 _15080 = float3(_15078, _15059.y, _14827);
运算过程详情:
  步骤1: tmp_0 = _15059.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15078, tmp_0, _14827)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15080 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第891行运算过程: _18103.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15080.xy, uint(rint(_15080.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15080.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15080.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18103.w = tmp_6
    操作数类型: half

==================================================
第892行运算过程: float3 _15092 = float3(_15078, _15059.y, _14850);
运算过程详情:
  步骤1: tmp_0 = _15059.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15078, tmp_0, _14850)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15092 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第893行运算过程: half3 _15097 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15092.xy, uint(rint(_15092.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _15092.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15092.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _15097 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第894行运算过程: float _15104 = _15074.x;
运算过程详情:
  步骤1: tmp_0 = _15074.x
    结果类型: float
    操作数类型: float

  步骤2: _15104 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第895行运算过程: float3 _15106 = float3(_15104, _15074.y, _14827);
运算过程详情:
  步骤1: tmp_0 = _15074.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15104, tmp_0, _14827)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15106 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第897行运算过程: _18105.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15106.xy, uint(rint(_15106.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15106.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15106.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18105.w = tmp_6
    操作数类型: half

==================================================
第898行运算过程: float3 _15118 = float3(_15104, _15074.y, _14850);
运算过程详情:
  步骤1: tmp_0 = _15074.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15104, tmp_0, _14850)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15118 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第899行运算过程: half3 _15123 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15118.xy, uint(rint(_15118.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _15118.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15118.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _15123 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第900行运算过程: half4 _15132 = half4(half(fast::clamp(_15021 - _15026, 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = _15021 - _15026
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half4(tmp_2)
    结果类型: half4
    操作数类型: half

  步骤5: _15132 = tmp_3
    结果类型: half4
    操作数类型: half4

==================================================
第901行运算过程: half4 _15133 = mix(half4(_15097.x, _15097.y, _15097.z, _18103.w), half4(_15123.x, _15123.y, _15123.z, _18105.w), _15132);
运算过程详情:
  步骤1: tmp_0 = _15097.x
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = _15097.y
    结果类型: half
    操作数类型: half

  步骤3: tmp_2 = _15097.z
    结果类型: half
    操作数类型: half

  步骤4: tmp_3 = _18103.w
    结果类型: unknown
    操作数类型: unknown

  步骤5: tmp_4 = half4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤6: tmp_5 = _15123.x
    结果类型: half
    操作数类型: half

  步骤7: tmp_6 = _15123.y
    结果类型: half
    操作数类型: half

  步骤8: tmp_7 = _15123.z
    结果类型: half
    操作数类型: half

  步骤9: tmp_8 = _18105.w
    结果类型: unknown
    操作数类型: unknown

  步骤10: tmp_9 = half4(tmp_5, tmp_6, tmp_7, tmp_8)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤11: tmp_10 = mix(tmp_4, tmp_9, _15132)
    结果类型: half4
    操作数类型: half4, half4, half4

  步骤12: _15133 = tmp_10
    结果类型: half4
    操作数类型: half4

==================================================
第902行运算过程: float3 _15140 = float3(_15078, _15059.y, _14831);
运算过程详情:
  步骤1: tmp_0 = _15059.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15078, tmp_0, _14831)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15140 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第904行运算过程: _18107.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15140.xy, uint(rint(_15140.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15140.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15140.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18107.w = tmp_6
    操作数类型: half

==================================================
第905行运算过程: float3 _15152 = float3(_15078, _15059.y, _14854);
运算过程详情:
  步骤1: tmp_0 = _15059.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15078, tmp_0, _14854)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15152 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第906行运算过程: half3 _15157 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15152.xy, uint(rint(_15152.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _15152.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15152.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _15157 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第907行运算过程: float3 _15166 = float3(_15104, _15074.y, _14831);
运算过程详情:
  步骤1: tmp_0 = _15074.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15104, tmp_0, _14831)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15166 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第909行运算过程: _18109.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15166.xy, uint(rint(_15166.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15166.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15166.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18109.w = tmp_6
    操作数类型: half

==================================================
第910行运算过程: float3 _15178 = float3(_15104, _15074.y, _14854);
运算过程详情:
  步骤1: tmp_0 = _15074.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15104, tmp_0, _14854)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15178 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第911行运算过程: half3 _15183 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15178.xy, uint(rint(_15178.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _15178.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15178.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _15183 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第912行运算过程: half4 _15193 = mix(half4(_15157.x, _15157.y, _15157.z, _18107.w), half4(_15183.x, _15183.y, _15183.z, _18109.w), _15132);
运算过程详情:
  步骤1: tmp_0 = _15157.x
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = _15157.y
    结果类型: half
    操作数类型: half

  步骤3: tmp_2 = _15157.z
    结果类型: half
    操作数类型: half

  步骤4: tmp_3 = _18107.w
    结果类型: unknown
    操作数类型: unknown

  步骤5: tmp_4 = half4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤6: tmp_5 = _15183.x
    结果类型: half
    操作数类型: half

  步骤7: tmp_6 = _15183.y
    结果类型: half
    操作数类型: half

  步骤8: tmp_7 = _15183.z
    结果类型: half
    操作数类型: half

  步骤9: tmp_8 = _18109.w
    结果类型: unknown
    操作数类型: unknown

  步骤10: tmp_9 = half4(tmp_5, tmp_6, tmp_7, tmp_8)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤11: tmp_10 = mix(tmp_4, tmp_9, _15132)
    结果类型: half4
    操作数类型: half4, half4, half4

  步骤12: _15193 = tmp_10
    结果类型: half4
    操作数类型: half4

==================================================
第913行运算过程: float3 _15200 = float3(_15078, _15059.y, _14833);
运算过程详情:
  步骤1: tmp_0 = _15059.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15078, tmp_0, _14833)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15200 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第915行运算过程: _18111.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15200.xy, uint(rint(_15200.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15200.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15200.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18111.w = tmp_6
    操作数类型: half

==================================================
第916行运算过程: float3 _15212 = float3(_15078, _15059.y, _14856);
运算过程详情:
  步骤1: tmp_0 = _15059.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15078, tmp_0, _14856)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15212 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第917行运算过程: half3 _15217 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15212.xy, uint(rint(_15212.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _15212.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15212.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _15217 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第918行运算过程: float3 _15226 = float3(_15104, _15074.y, _14833);
运算过程详情:
  步骤1: tmp_0 = _15074.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15104, tmp_0, _14833)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15226 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第920行运算过程: _18113.w = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15226.xy, uint(rint(_15226.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15226.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15226.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18113.w = tmp_6
    操作数类型: half

==================================================
第921行运算过程: float3 _15238 = float3(_15104, _15074.y, _14856);
运算过程详情:
  步骤1: tmp_0 = _15074.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15104, tmp_0, _14856)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15238 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第922行运算过程: half3 _15243 = half3(sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, _15238.xy, uint(rint(_15238.z)), level(0.0)).xyz);
运算过程详情:
  步骤1: tmp_0 = _15238.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15238.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAORGBVTSampler.sample(sSHAORGBVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half3(tmp_5)
    结果类型: half3
    操作数类型: float

  步骤8: _15243 = tmp_6
    结果类型: half3
    操作数类型: half3

==================================================
第923行运算过程: half4 _15253 = mix(half4(_15217.x, _15217.y, _15217.z, _18111.w), half4(_15243.x, _15243.y, _15243.z, _18113.w), _15132);
运算过程详情:
  步骤1: tmp_0 = _15217.x
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = _15217.y
    结果类型: half
    操作数类型: half

  步骤3: tmp_2 = _15217.z
    结果类型: half
    操作数类型: half

  步骤4: tmp_3 = _18111.w
    结果类型: unknown
    操作数类型: unknown

  步骤5: tmp_4 = half4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤6: tmp_5 = _15243.x
    结果类型: half
    操作数类型: half

  步骤7: tmp_6 = _15243.y
    结果类型: half
    操作数类型: half

  步骤8: tmp_7 = _15243.z
    结果类型: half
    操作数类型: half

  步骤9: tmp_8 = _18113.w
    结果类型: unknown
    操作数类型: unknown

  步骤10: tmp_9 = half4(tmp_5, tmp_6, tmp_7, tmp_8)
    结果类型: half4
    操作数类型: half, half, half, unknown

  步骤11: tmp_10 = mix(tmp_4, tmp_9, _15132)
    结果类型: half4
    操作数类型: half4, half4, half4

  步骤12: _15253 = tmp_10
    结果类型: half4
    操作数类型: half4

==================================================
第924行运算过程: half _15255 = half(32.0);
运算过程详情:
  步骤1: tmp_0 = half(32.0)
    结果类型: half
    操作数类型: float

  步骤2: _15255 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第925行运算过程: half _15258 = _15133.w * _15255;
运算过程详情:
  步骤1: tmp_0 = _15133.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * _15255
    结果类型: half
    操作数类型: half, half

  步骤3: _15258 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第926行运算过程: half _15262 = _15193.w * _15255;
运算过程详情:
  步骤1: tmp_0 = _15193.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * _15255
    结果类型: half
    操作数类型: half, half

  步骤3: _15262 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第927行运算过程: half _15266 = _15253.w * _15255;
运算过程详情:
  步骤1: tmp_0 = _15253.w
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * _15255
    结果类型: half
    操作数类型: half, half

  步骤3: _15266 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第928行运算过程: half3 _15343 = half3(((float3(_15133.xyz) * float3(2.0)) - float3(1.0)) * float(_15258)).xyz;
运算过程详情:
  步骤1: tmp_0 = _15133.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = float3(2.0)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: tmp_4 = float3(1.0)
    结果类型: float3
    操作数类型: float

  步骤6: tmp_5 = tmp_3 - tmp_4
    结果类型: float3
    操作数类型: float3, float3

  步骤7: tmp_6 = float(_15258)
    结果类型: float
    操作数类型: half

  步骤8: tmp_7 = tmp_5 * tmp_6
    结果类型: float
    操作数类型: float3, float

  步骤9: tmp_8 = half3(tmp_7)
    结果类型: half3
    操作数类型: float

  步骤10: _15343 = tmp_8
    结果类型: half3
    操作数类型: half3

==================================================
第930行运算过程: _18130.x = half(float(dot(_14556, _15343)) * 2.0);
运算过程详情:
  步骤1: tmp_0 = dot(_14556, _15343)
    结果类型: float
    操作数类型: half3, half3

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_1 * 2.0
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: _18130.x = tmp_3
    操作数类型: half

==================================================
第931行运算过程: half3 _15352 = half3(((float3(_15193.xyz) * float3(2.0)) - float3(1.0)) * float(_15262)).xyz;
运算过程详情:
  步骤1: tmp_0 = _15193.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = float3(2.0)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: tmp_4 = float3(1.0)
    结果类型: float3
    操作数类型: float

  步骤6: tmp_5 = tmp_3 - tmp_4
    结果类型: float3
    操作数类型: float3, float3

  步骤7: tmp_6 = float(_15262)
    结果类型: float
    操作数类型: half

  步骤8: tmp_7 = tmp_5 * tmp_6
    结果类型: float
    操作数类型: float3, float

  步骤9: tmp_8 = half3(tmp_7)
    结果类型: half3
    操作数类型: float

  步骤10: _15352 = tmp_8
    结果类型: half3
    操作数类型: half3

==================================================
第932行运算过程: _18130.y = half(float(dot(_14556, _15352)) * 2.0);
运算过程详情:
  步骤1: tmp_0 = dot(_14556, _15352)
    结果类型: float
    操作数类型: half3, half3

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_1 * 2.0
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: _18130.y = tmp_3
    操作数类型: half

==================================================
第933行运算过程: half3 _15361 = half3(((float3(_15253.xyz) * float3(2.0)) - float3(1.0)) * float(_15266)).xyz;
运算过程详情:
  步骤1: tmp_0 = _15253.xyz
    结果类型: half3
    操作数类型: half3

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = float3(2.0)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: tmp_4 = float3(1.0)
    结果类型: float3
    操作数类型: float

  步骤6: tmp_5 = tmp_3 - tmp_4
    结果类型: float3
    操作数类型: float3, float3

  步骤7: tmp_6 = float(_15266)
    结果类型: float
    操作数类型: half

  步骤8: tmp_7 = tmp_5 * tmp_6
    结果类型: float
    操作数类型: float3, float

  步骤9: tmp_8 = half3(tmp_7)
    结果类型: half3
    操作数类型: float

  步骤10: _15361 = tmp_8
    结果类型: half3
    操作数类型: half3

==================================================
第934行运算过程: _18130.z = half(float(dot(_14556, _15361)) * 2.0);
运算过程详情:
  步骤1: tmp_0 = dot(_14556, _15361)
    结果类型: float
    操作数类型: half3, half3

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_1 * 2.0
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: _18130.z = tmp_3
    操作数类型: half

==================================================
第936行运算过程: if (_8590)
  无运算过程
==================================================
第938行运算过程: _18819 = half3(((float3(_15343) * float3(0.21199999749660491943359375)) + (float3(_15352) * float3(0.714999973773956298828125))) + (float3(_15361) * float3(0.0719999969005584716796875)));
运算过程详情:
  步骤1: tmp_0 = float3(_15343)
    结果类型: float3
    操作数类型: half3

  步骤2: tmp_1 = float3(0.21199999749660491943359375)
    结果类型: float3
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: tmp_3 = float3(_15352)
    结果类型: float3
    操作数类型: half3

  步骤5: tmp_4 = float3(0.714999973773956298828125)
    结果类型: float3
    操作数类型: float

  步骤6: tmp_5 = tmp_3 * tmp_4
    结果类型: float3
    操作数类型: float3, float3

  步骤7: tmp_6 = tmp_2 + tmp_5
    结果类型: float3
    操作数类型: float3, float3

  步骤8: tmp_7 = float3(_15361)
    结果类型: float3
    操作数类型: half3

  步骤9: tmp_8 = float3(0.0719999969005584716796875)
    结果类型: float3
    操作数类型: float

  步骤10: tmp_9 = tmp_7 * tmp_8
    结果类型: float3
    操作数类型: float3, float3

  步骤11: tmp_10 = tmp_6 + tmp_9
    结果类型: float3
    操作数类型: float3, float3

  步骤12: tmp_11 = half3(tmp_10)
    结果类型: half3
    操作数类型: float3

  步骤13: _18819 = tmp_11
    操作数类型: half3

==================================================
第942行运算过程: _18819 = _8393;
运算过程详情:
  步骤1: _18819 = _8393
    操作数类型: half3

==================================================
第944行运算过程: _18818 = _18819;
运算过程详情:
  步骤1: _18818 = _18819
    操作数类型: unknown

==================================================
第945行运算过程: _18806 = max(half3(_15258, _15262, _15266) + (_18130 * half(mix(_Block1.SHGIParam2.z, 1.0, _14482))), _8393);
运算过程详情:
  步骤1: tmp_0 = half3(_15258, _15262, _15266)
    结果类型: half3
    操作数类型: half, half, half

  步骤2: tmp_1 = _Block1.SHGIParam2.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = mix(tmp_1, 1.0, _14482)
    结果类型: float
    操作数类型: float, float, float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = _18130 * tmp_3
    结果类型: half
    操作数类型: unknown, half

  步骤6: tmp_5 = tmp_0 + tmp_4
    结果类型: half
    操作数类型: half3, half

  步骤7: tmp_6 = max(tmp_5, _8393)
    结果类型: half3
    操作数类型: half, half3

  步骤8: _18806 = tmp_6
    操作数类型: half3

==================================================
第949行运算过程: float2 _15427 = ((_14747.xz - float2(0.5)) * 0.9375) + float2(0.5);
运算过程详情:
  步骤1: tmp_0 = _14747.xz
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float2
    操作数类型: float2, float2

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float2, float

  步骤5: tmp_4 = float2(0.5)
    结果类型: float2
    操作数类型: float

  步骤6: tmp_5 = tmp_3 + tmp_4
    结果类型: float
    操作数类型: float, float2

  步骤7: _15427 = tmp_5
    结果类型: float2
    操作数类型: float

==================================================
第950行运算过程: float _15434 = _14717.x * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _15434 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第951行运算过程: float _15438 = ((_15434 - floor(_15434)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_15434)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _15434 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _15438 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第952行运算过程: float _15444 = _14717.z * 0.0041666668839752674102783203125;
运算过程详情:
  步骤1: tmp_0 = _14717.z
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 0.0041666668839752674102783203125
    结果类型: float
    操作数类型: float, float

  步骤3: _15444 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第953行运算过程: float _15448 = ((_15444 - floor(_15444)) - 0.5) * 0.9375;
运算过程详情:
  步骤1: tmp_0 = floor(_15444)
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _15444 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.9375
    结果类型: float
    操作数类型: float, float

  步骤5: _15448 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第954行运算过程: float _15453 = _15427.x;
运算过程详情:
  步骤1: tmp_0 = _15427.x
    结果类型: float
    操作数类型: float

  步骤2: _15453 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第956行运算过程: _18143.x = (_15453 < (_15438 + 0.5)) ? fast::min(_15453, _15438 + 0.49609375) : fast::max(_15453, _15438 + 0.50390625);
运算过程详情:
  步骤1: _18143.x = _15453
    操作数类型: float

==================================================
第957行运算过程: float _15471 = _15427.y;
运算过程详情:
  步骤1: tmp_0 = _15427.y
    结果类型: float
    操作数类型: float

  步骤2: _15471 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第958行运算过程: _18143.z = (_15471 < (_15448 + 0.5)) ? fast::min(_15471, _15448 + 0.49609375) : fast::max(_15471, _15448 + 0.50390625);
运算过程详情:
  步骤1: _18143.z = _15471
    操作数类型: float

==================================================
第959行运算过程: float _15492 = (_14747.y * 64.0) - 0.5;
运算过程详情:
  步骤1: tmp_0 = _14747.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * 64.0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - 0.5
    结果类型: float
    操作数类型: float, float

  步骤4: _15492 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第960行运算过程: float _15497 = floor(_15492);
运算过程详情:
  步骤1: tmp_0 = floor(_15492)
    结果类型: float
    操作数类型: float

  步骤2: _15497 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第961行运算过程: uint _15500 = (_15492 < 0.0) ? 63u : uint(_15497);
运算过程详情:
  步骤1: _15500 = _15492
    结果类型: uint
    操作数类型: float

==================================================
第962行运算过程: uint _15503 = _15500 + 1u;
运算过程详情:
  步骤1: tmp_0 = _15500 + 1
    结果类型: int
    操作数类型: uint, int

  步骤2: _15503 = tmp_0
    结果类型: uint
    操作数类型: int

==================================================
第963行运算过程: uint _21308 = (_15503 >= 64u) ? 0u : _15503;
  无运算过程
==================================================
第964行运算过程: float2 _15530 = (float2(float(_15500 & 7u), float(_15500 >> 3u)) + _18143.xz) * 0.125;
运算过程详情:
  步骤1: tmp_0 = float(_15500)
    结果类型: float
    操作数类型: uint

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _15530 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第965行运算过程: float2 _15545 = (float2(float(_21308 & 7u), float(_21308 >> 3u)) + _18143.xz) * 0.125;
运算过程详情:
  步骤1: tmp_0 = float(_21308)
    结果类型: float
    操作数类型: unknown

  步骤2: tmp_1 = float2(tmp_0)
    结果类型: float2
    操作数类型: float

  步骤3: _15545 = tmp_1
    结果类型: float2
    操作数类型: float2

==================================================
第966行运算过程: float _15549 = _15530.x;
运算过程详情:
  步骤1: tmp_0 = _15530.x
    结果类型: float
    操作数类型: float

  步骤2: _15549 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第967行运算过程: float3 _15551 = float3(_15549, _15530.y, _14827);
运算过程详情:
  步骤1: tmp_0 = _15530.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15549, tmp_0, _14827)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15551 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第969行运算过程: _18151.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15551.xy, uint(rint(_15551.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15551.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15551.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18151.x = tmp_6
    操作数类型: half

==================================================
第970行运算过程: float _15561 = _15545.x;
运算过程详情:
  步骤1: tmp_0 = _15545.x
    结果类型: float
    操作数类型: float

  步骤2: _15561 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第971行运算过程: float3 _15563 = float3(_15561, _15545.y, _14827);
运算过程详情:
  步骤1: tmp_0 = _15545.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15561, tmp_0, _14827)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15563 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第973行运算过程: _18153.x = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15563.xy, uint(rint(_15563.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15563.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15563.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18153.x = tmp_6
    操作数类型: half

==================================================
第974行运算过程: float3 _15575 = float3(_15549, _15530.y, _14831);
运算过程详情:
  步骤1: tmp_0 = _15530.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15549, tmp_0, _14831)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15575 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第975行运算过程: _18151.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15575.xy, uint(rint(_15575.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15575.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15575.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18151.y = tmp_6
    操作数类型: half

==================================================
第976行运算过程: float3 _15587 = float3(_15561, _15545.y, _14831);
运算过程详情:
  步骤1: tmp_0 = _15545.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15561, tmp_0, _14831)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15587 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第977行运算过程: _18153.y = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15587.xy, uint(rint(_15587.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15587.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15587.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18153.y = tmp_6
    操作数类型: half

==================================================
第978行运算过程: float3 _15599 = float3(_15549, _15530.y, _14833);
运算过程详情:
  步骤1: tmp_0 = _15530.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15549, tmp_0, _14833)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15599 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第979行运算过程: _18151.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15599.xy, uint(rint(_15599.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15599.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15599.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18151.z = tmp_6
    操作数类型: half

==================================================
第980行运算过程: float3 _15611 = float3(_15561, _15545.y, _14833);
运算过程详情:
  步骤1: tmp_0 = _15545.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(_15561, tmp_0, _14833)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: _15611 = tmp_1
    结果类型: float3
    操作数类型: float3

==================================================
第981行运算过程: _18153.z = half(sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, _15611.xy, uint(rint(_15611.z)), level(0.0)).x);
运算过程详情:
  步骤1: tmp_0 = _15611.xy
    结果类型: float2
    操作数类型: float2

  步骤2: tmp_1 = _15611.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = rint(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = uint(tmp_2)
    结果类型: uint
    操作数类型: float

  步骤5: tmp_4 = level(0.0)
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = sSHAOAlphaVTSampler.sample(sSHAOAlphaVTSamplerSmplr, tmp_0, tmp_3, tmp_4)
    结果类型: float
    操作数类型: unknown, float2, uint, float

  步骤7: tmp_6 = half(tmp_5)
    结果类型: half
    操作数类型: float

  步骤8: _18153.z = tmp_6
    操作数类型: half

==================================================
第982行运算过程: half3 _15622 = mix(_18151, _18153, half3(half(fast::clamp(_15492 - _15497, 0.0, 1.0))));
运算过程详情:
  步骤1: tmp_0 = _15492 - _15497
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_2)
    结果类型: half3
    操作数类型: half

  步骤5: tmp_4 = mix(_18151, _18153, tmp_3)
    结果类型: unknown
    操作数类型: unknown, unknown, half3

  步骤6: _15622 = tmp_4
    结果类型: half3
    操作数类型: unknown

==================================================
第983行运算过程: half _15623 = half(32.0);
运算过程详情:
  步骤1: tmp_0 = half(32.0)
    结果类型: half
    操作数类型: float

  步骤2: _15623 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第985行运算过程: _18164.x = _15622.x * _15623;
运算过程详情:
  步骤1: tmp_0 = _15622.x
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * _15623
    结果类型: half
    操作数类型: half, half

  步骤3: _18164.x = tmp_1
    操作数类型: half

==================================================
第986行运算过程: _18164.y = _15622.y * _15623;
运算过程详情:
  步骤1: tmp_0 = _15622.y
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * _15623
    结果类型: half
    操作数类型: half, half

  步骤3: _18164.y = tmp_1
    操作数类型: half

==================================================
第987行运算过程: _18164.z = _15622.z * _15623;
运算过程详情:
  步骤1: tmp_0 = _15622.z
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 * _15623
    结果类型: half
    操作数类型: half, half

  步骤3: _18164.z = tmp_1
    操作数类型: half

==================================================
第988行运算过程: _18818 = _8393;
运算过程详情:
  步骤1: _18818 = _8393
    操作数类型: half3

==================================================
第989行运算过程: _18806 = _18164;
运算过程详情:
  步骤1: _18806 = _18164
    操作数类型: unknown

==================================================
第991行运算过程: float3 _15658 = (((in.IN_WorldPosition.xyz - _14717) * float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)) * 2.0) - float3(1.0);
运算过程详情:
  步骤1: tmp_0 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = tmp_0 - _14717
    结果类型: float3
    操作数类型: float3, float3

  步骤3: tmp_2 = float3(0.0041666668839752674102783203125, 0.0078125, 0.0041666668839752674102783203125)
    结果类型: float3
    操作数类型: float, float, float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: tmp_4 = tmp_3 * 2.0
    结果类型: float
    操作数类型: float3, float

  步骤6: tmp_5 = float3(1.0)
    结果类型: float3
    操作数类型: float

  步骤7: tmp_6 = tmp_4 - tmp_5
    结果类型: float
    操作数类型: float, float3

  步骤8: _15658 = tmp_6
    结果类型: float3
    操作数类型: float

==================================================
第992行运算过程: float3 _15661 = _15658 * _15658;
运算过程详情:
  步骤1: tmp_0 = _15658 * _15658
    结果类型: float3
    操作数类型: float3, float3

  步骤2: _15661 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第993行运算过程: float3 _15664 = _15661 * _15661;
运算过程详情:
  步骤1: tmp_0 = _15661 * _15661
    结果类型: float3
    操作数类型: float3, float3

  步骤2: _15664 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第996行运算过程: if ((max(int(_14451), 2) == 3) && ((_8397 & 32768u) > 0u))
  无运算过程
==================================================
第998行运算过程: half3 _14921 = half3(half(1.0 - fast::clamp(fast::max(_15664.x, fast::max(_15664.y, _15664.z)), 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = _15664.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _15664.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _15664.z
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = fast::max(tmp_1, tmp_2)
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::max(tmp_0, tmp_3)
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::clamp(tmp_4, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤7: tmp_6 = 1.0 - tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = half(tmp_6)
    结果类型: half
    操作数类型: float

  步骤9: tmp_8 = half3(tmp_7)
    结果类型: half3
    操作数类型: half

  步骤10: _14921 = tmp_8
    结果类型: half3
    操作数类型: half3

==================================================
第999行运算过程: _18823 = _18818 * _14921;
运算过程详情:
  步骤1: tmp_0 = _18818 * _14921
    结果类型: half3
    操作数类型: unknown, half3

  步骤2: _18823 = tmp_0
    操作数类型: half3

==================================================
第1000行运算过程: _18822 = _18806 * _14921;
运算过程详情:
  步骤1: tmp_0 = _18806 * _14921
    结果类型: half3
    操作数类型: unknown, half3

  步骤2: _18822 = tmp_0
    操作数类型: half3

==================================================
第1004行运算过程: _18823 = _18818;
运算过程详情:
  步骤1: _18823 = _18818
    操作数类型: unknown

==================================================
第1005行运算过程: _18822 = _18806;
运算过程详情:
  步骤1: _18822 = _18806
    操作数类型: unknown

==================================================
第1007行运算过程: _18827 = _18823;
运算过程详情:
  步骤1: _18827 = _18823
    操作数类型: unknown

==================================================
第1008行运算过程: _18825 = _18822;
运算过程详情:
  步骤1: _18825 = _18822
    操作数类型: unknown

==================================================
第1012行运算过程: _18827 = _8393;
运算过程详情:
  步骤1: _18827 = _8393
    操作数类型: half3

==================================================
第1013行运算过程: _18825 = _8393;
运算过程详情:
  步骤1: _18825 = _8393
    操作数类型: half3

==================================================
第1015行运算过程: _18826 = _18827;
运算过程详情:
  步骤1: _18826 = _18827
    操作数类型: unknown

==================================================
第1016行运算过程: _18824 = _18825;
运算过程详情:
  步骤1: _18824 = _18825
    操作数类型: unknown

==================================================
第1020行运算过程: _18826 = _8393;
运算过程详情:
  步骤1: _18826 = _8393
    操作数类型: half3

==================================================
第1021行运算过程: _18824 = _8393;
运算过程详情:
  步骤1: _18824 = _8393
    操作数类型: half3

==================================================
第1023行运算过程: half3 _14565 = half3(float3(0.0));
运算过程详情:
  步骤1: tmp_0 = float3(0.0)
    结果类型: float3
    操作数类型: float

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float3

  步骤3: _14565 = tmp_1
    结果类型: half3
    操作数类型: half3

==================================================
第1025行运算过程: if (_8590)
  无运算过程
==================================================
第1027行运算过程: float3 _14569 = float3(_18824);
运算过程详情:
  步骤1: tmp_0 = float3(_18824)
    结果类型: float3
    操作数类型: unknown

  步骤2: _14569 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第1028行运算过程: float _14575 = float(dot(_18826, _18826));
运算过程详情:
  步骤1: tmp_0 = dot(_18826, _18826)
    结果类型: float
    操作数类型: unknown, unknown

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: float

  步骤3: _14575 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第1030行运算过程: if ((float(half(((0.21267099678516387939453125 * _14569.x) + (0.71516001224517822265625 * _14569.y)) + (0.072168998420238494873046875 * _14569.z))) > 0.001000000047497451305389404296875) && (_14575 > 9.9999999747524270787835121154785e-07))
  无运算过程
==================================================
第1032行运算过程: float _14592 = fast::clamp(_9109.y, 0.0, 1.0) * 0.75;
运算过程详情:
  步骤1: tmp_0 = _9109.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = tmp_1 * 0.75
    结果类型: float
    操作数类型: float, float

  步骤4: _14592 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第1033行运算过程: float3 _14600 = (float3(_18826) / float3(sqrt(_14575))) * float3(fast::clamp(1.0 - (_14592 * _14592), 0.0, 1.0));
运算过程详情:
  步骤1: tmp_0 = float3(_18826)
    结果类型: float3
    操作数类型: unknown

  步骤2: tmp_1 = sqrt(_14575)
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = float3(tmp_1)
    结果类型: float3
    操作数类型: float

  步骤4: tmp_3 = tmp_0 / tmp_2
    结果类型: float3
    操作数类型: float3, float3

  步骤5: tmp_4 = _14592 * _14592
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = 1.0 - tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤8: tmp_7 = float3(tmp_6)
    结果类型: float3
    操作数类型: float

  步骤9: tmp_8 = tmp_3 * tmp_7
    结果类型: float3
    操作数类型: float3, float3

  步骤10: _14600 = tmp_8
    结果类型: float3
    操作数类型: float3

==================================================
第1034行运算过程: float _14604 = mix(_11560, 1.0, 0.25);
运算过程详情:
  步骤1: tmp_0 = mix(_11560, 1.0, 0.25)
    结果类型: float
    操作数类型: float, float, float

  步骤2: _14604 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1035行运算过程: float _14612 = fast::clamp(dot(_9109, fast::normalize(_8373 + _14600)), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _8373 + _14600
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = dot(_9109, tmp_1)
    结果类型: float
    操作数类型: float3, float3

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _14612 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第1036行运算过程: float _15697 = _14604 * _14604;
运算过程详情:
  步骤1: tmp_0 = _14604 * _14604
    结果类型: float
    操作数类型: float, float

  步骤2: _15697 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1037行运算过程: float _15710 = _15697 / (((((_14612 * _15697) * _15697) - _14612) * _14612) + 1.0);
运算过程详情:
  步骤1: tmp_0 = _14612 * _15697
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * _15697
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - _14612
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _14612
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_3 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _15697 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _15710 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第1038行运算过程: _18831 = half3(float3(_10557 * _18824) * float3((fast::min(1000.0, (_15710 * _15710) * 0.3183098733425140380859375) * fast::clamp(dot(_9109, _14600), 0.0, 1.0)) * _Block1.SHGIParam.y));
运算过程详情:
  步骤1: tmp_0 = _10557 * _18824
    结果类型: half3
    操作数类型: half3, unknown

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: half3

  步骤3: tmp_2 = _15710 * _15710
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * 0.3183098733425140380859375
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = fast::min(1000.0, tmp_3)
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = dot(_9109, _14600)
    结果类型: float
    操作数类型: float3, float3

  步骤7: tmp_6 = fast::clamp(tmp_5, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤8: tmp_7 = tmp_4 * tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = _Block1.SHGIParam.y
    结果类型: float
    操作数类型: float

  步骤10: tmp_9 = tmp_7 * tmp_8
    结果类型: float
    操作数类型: float, float

  步骤11: tmp_10 = float3(tmp_9)
    结果类型: float3
    操作数类型: float

  步骤12: tmp_11 = tmp_1 * tmp_10
    结果类型: float3
    操作数类型: float3, float3

  步骤13: tmp_12 = half3(tmp_11)
    结果类型: half3
    操作数类型: float3

  步骤14: _18831 = tmp_12
    操作数类型: half3

==================================================
第1042行运算过程: _18831 = _14565;
运算过程详情:
  步骤1: _18831 = _14565
    操作数类型: half3

==================================================
第1044行运算过程: _18830 = _18831;
运算过程详情:
  步骤1: _18830 = _18831
    操作数类型: unknown

==================================================
第1048行运算过程: _18830 = _14565;
运算过程详情:
  步骤1: _18830 = _14565
    操作数类型: half3

==================================================
第1050行运算过程: float _14641 = float(half(mix(_Block1.SHGIParam2.y, 1.0, _14482)));
运算过程详情:
  步骤1: tmp_0 = _Block1.SHGIParam2.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = mix(tmp_0, 1.0, _14482)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: _14641 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第1051行运算过程: _18859 = _18830 * half3(half(_Block1.SHGIParam.y * _14641));
运算过程详情:
  步骤1: tmp_0 = _Block1.SHGIParam.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * _14641
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_2)
    结果类型: half3
    操作数类型: half

  步骤5: tmp_4 = _18830 * tmp_3
    结果类型: half3
    操作数类型: unknown, half3

  步骤6: _18859 = tmp_4
    操作数类型: half3

==================================================
第1052行运算过程: _18832 = _18824 * half3(half(_Block1.SHGIParam.x * _14641));
运算过程详情:
  步骤1: tmp_0 = _Block1.SHGIParam.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = tmp_0 * _14641
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_2)
    结果类型: half3
    操作数类型: half

  步骤5: tmp_4 = _18824 * tmp_3
    结果类型: half3
    操作数类型: unknown, half3

  步骤6: _18832 = tmp_4
    操作数类型: half3

==================================================
第1056行运算过程: _18859 = _8393;
运算过程详情:
  步骤1: _18859 = _8393
    操作数类型: half3

==================================================
第1057行运算过程: _18832 = _8393;
运算过程详情:
  步骤1: _18832 = _8393
    操作数类型: half3

==================================================
第1059行运算过程: _19028 = _8560 + (_18832 * _18329);
运算过程详情:
  步骤1: tmp_0 = _18832 * _18329
    结果类型: unknown
    操作数类型: unknown, unknown

  步骤2: tmp_1 = _8560 + tmp_0
    结果类型: half3
    操作数类型: half3, unknown

  步骤3: _19028 = tmp_1
    操作数类型: half3

==================================================
第1060行运算过程: _18989 = _8565 + float3(_18859 * _18329);
运算过程详情:
  步骤1: tmp_0 = _18859 * _18329
    结果类型: unknown
    操作数类型: unknown, unknown

  步骤2: tmp_1 = float3(tmp_0)
    结果类型: float3
    操作数类型: unknown

  步骤3: tmp_2 = _8565 + tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _18989 = tmp_2
    操作数类型: float3

==================================================
第1064行运算过程: _19028 = _8560;
运算过程详情:
  步骤1: _19028 = _8560
    操作数类型: half3

==================================================
第1065行运算过程: _18989 = _8565;
运算过程详情:
  步骤1: _18989 = _8565
    操作数类型: float3

==================================================
第1067行运算过程: float _15792 = fast::clamp(dot(_9109, fast::normalize(_8373 + _8373)), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _8373 + _8373
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = dot(_9109, tmp_1)
    结果类型: float
    操作数类型: float3, float3

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _15792 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第1068行运算过程: float _15839 = _12049 / (((((_15792 * _12049) * _12049) - _15792) * _15792) + 1.0);
运算过程详情:
  步骤1: tmp_0 = _15792 * _12049
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * _12049
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - _15792
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _15792
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_3 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _12049 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _15839 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第1069行运算过程: float _15805 = float(half(fast::clamp(_8378, 0.0, 1.0)));
运算过程详情:
  步骤1: tmp_0 = fast::clamp(_8378, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = float(tmp_1)
    结果类型: float
    操作数类型: half

  步骤4: _15805 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第1070行运算过程: float _15854 = _12049 * 0.5;
运算过程详情:
  步骤1: tmp_0 = _12049 * 0.5
    结果类型: float
    操作数类型: float, float

  步骤2: _15854 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1071行运算过程: float _15858 = 1.0 - _15854;
运算过程详情:
  步骤1: tmp_0 = 1.0 - _15854
    结果类型: float
    操作数类型: float, float

  步骤2: _15858 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1072行运算过程: float _15861 = (_15805 * _15858) + _15854;
运算过程详情:
  步骤1: tmp_0 = _15805 * _15858
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 + _15854
    结果类型: float
    操作数类型: float, float

  步骤3: _15861 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第1073行运算过程: half3 _15813 = half3(_12025);
运算过程详情:
  步骤1: tmp_0 = half3(_12025)
    结果类型: half3
    操作数类型: float3

  步骤2: _15813 = tmp_0
    结果类型: half3
    操作数类型: half3

==================================================
第1074行运算过程: float3 _15762 = float3(_10569);
运算过程详情:
  步骤1: tmp_0 = float3(_10569)
    结果类型: float3
    操作数类型: half3

  步骤2: _15762 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第1075行运算过程: float3 _15920 = _Block1.cLocalVirtualLitPos.xyz + _Block1.cVirtualLitParam.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.cLocalVirtualLitPos.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.cVirtualLitParam.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = tmp_0 + tmp_1
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _15920 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第1076行运算过程: float3 _15960 = ((((fast::normalize(cross(_8373, float3(0.0, 1.0, 0.0))) * _15920.x) + float3(0.0, _15920.y, 0.0)) + (_8373 * _15920.z)) + (float4(0.0, 0.0, 0.0, 1.0) * _Block1.World)) - in.IN_WorldPosition.xyz;
运算过程详情:
  步骤1: tmp_0 = float3(0.0, 1.0, 0.0)
    结果类型: float3
    操作数类型: float, float, float

  步骤2: tmp_1 = cross(_8373, tmp_0)
    结果类型: float3
    操作数类型: float3, float3

  步骤3: tmp_2 = fast::normalize(tmp_1)
    结果类型: float3
    操作数类型: float3

  步骤4: tmp_3 = _15920.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = tmp_2 * tmp_3
    结果类型: float
    操作数类型: float3, float

  步骤6: tmp_5 = _15920.y
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = float3(0.0, tmp_5, 0.0)
    结果类型: float3
    操作数类型: float, float, float

  步骤8: tmp_7 = tmp_4 + tmp_6
    结果类型: float
    操作数类型: float, float3

  步骤9: tmp_8 = _15920.z
    结果类型: float
    操作数类型: float

  步骤10: tmp_9 = _8373 * tmp_8
    结果类型: float
    操作数类型: float3, float

  步骤11: tmp_10 = tmp_7 + tmp_9
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = float4(0.0, 0.0, 0.0, 1.0)
    结果类型: float4
    操作数类型: float, float, float, float

  步骤13: tmp_12 = _Block1.World
    结果类型: float3x4
    操作数类型: float3x4

  步骤14: tmp_13 = tmp_11 * tmp_12
    结果类型: float4
    操作数类型: float4, float3x4

  步骤15: tmp_14 = tmp_10 + tmp_13
    结果类型: float
    操作数类型: float, float4

  步骤16: tmp_15 = in.IN_WorldPosition.xyz
    结果类型: float3
    操作数类型: float3

  步骤17: tmp_16 = tmp_14 - tmp_15
    结果类型: float
    操作数类型: float, float3

  步骤18: _15960 = tmp_16
    结果类型: float3
    操作数类型: float

==================================================
第1077行运算过程: float3 _15970 = mix(_15960, fast::normalize(_15960), float3(step(0.0, _Block1.cLocalVirtualLitColor.w)));
运算过程详情:
  步骤1: tmp_0 = fast::normalize(_15960)
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = _Block1.cLocalVirtualLitColor.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = step(0.0, tmp_1)
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = float3(tmp_2)
    结果类型: float3
    操作数类型: float

  步骤5: tmp_4 = mix(_15960, tmp_0, tmp_3)
    结果类型: float3
    操作数类型: float3, float3, float3

  步骤6: _15970 = tmp_4
    结果类型: float3
    操作数类型: float3

==================================================
第1078行运算过程: half _15974 = half(dot(_15970, _15970));
运算过程详情:
  步骤1: tmp_0 = dot(_15970, _15970)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: _15974 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第1079行运算过程: float3 _15976 = fast::normalize(_15970);
运算过程详情:
  步骤1: tmp_0 = fast::normalize(_15970)
    结果类型: float3
    操作数类型: float3

  步骤2: _15976 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第1080行运算过程: half _15979 = _15974 * half(1.0 / (_Block1.cLocalVirtualLitCustom.x * _Block1.cLocalVirtualLitCustom.x));
运算过程详情:
  步骤1: tmp_0 = _Block1.cLocalVirtualLitCustom.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.cLocalVirtualLitCustom.x
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = 1.0 / tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float

  步骤6: tmp_5 = _15974 * tmp_4
    结果类型: half
    操作数类型: half, half

  步骤7: _15979 = tmp_5
    结果类型: half
    操作数类型: half

==================================================
第1081行运算过程: float _15986 = fast::clamp(1.0 - float(_15979 * _15979), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _15979 * _15979
    结果类型: half
    操作数类型: half, half

  步骤2: tmp_1 = float(tmp_0)
    结果类型: float
    操作数类型: half

  步骤3: tmp_2 = 1.0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _15986 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第1082行运算过程: half _16031 = half((fast::clamp(dot(_9109, _15976), 0.0, 1.0) * _Block1.cLocalVirtualLitCustom.z) + (1.0 - _Block1.cLocalVirtualLitCustom.z));
运算过程详情:
  步骤1: tmp_0 = dot(_9109, _15976)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::clamp(tmp_0, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤3: tmp_2 = _Block1.cLocalVirtualLitCustom.z
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = tmp_1 * tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = _Block1.cLocalVirtualLitCustom.z
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = 1.0 - tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = tmp_3 + tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = half(tmp_6)
    结果类型: half
    操作数类型: float

  步骤9: _16031 = tmp_7
    结果类型: half
    操作数类型: half

==================================================
第1083行运算过程: float _16112 = fast::clamp(dot(_9109, fast::normalize(_8373 + _15976)), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _8373 + _15976
    结果类型: float3
    操作数类型: float3, float3

  步骤2: tmp_1 = fast::normalize(tmp_0)
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = dot(_9109, tmp_1)
    结果类型: float
    操作数类型: float3, float3

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _16112 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第1084行运算过程: float _16159 = _12049 / (((((_16112 * _12049) * _12049) - _16112) * _16112) + 1.0);
运算过程详情:
  步骤1: tmp_0 = _16112 * _12049
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * _12049
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_1 - _16112
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = tmp_2 * _16112
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_3 + 1.0
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = _12049 / tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: _16159 = tmp_5
    结果类型: float
    操作数类型: float

==================================================
第1085行运算过程: float _8657 = float(_18329);
运算过程详情:
  步骤1: tmp_0 = float(_18329)
    结果类型: float
    操作数类型: unknown

  步骤2: _8657 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1086行运算过程: half _8696 = half(fast::min(_8657, mix(abs(_Block1.cVisibilitySH[0].w), 1.0, _Block1.WorldProbeInfo.x)));
运算过程详情:
  步骤1: tmp_0 = _Block1.cVisibilitySH
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = abs(tmp_0)
    结果类型: float4
    操作数类型: float4

  步骤3: tmp_2 = mix(tmp_1)
    结果类型: float4
    操作数类型: float4

  步骤4: tmp_3 = fast::min(_8657, tmp_2)
    结果类型: float4
    操作数类型: float, float4

  步骤5: tmp_4 = half(tmp_3)
    结果类型: half
    操作数类型: float4

  步骤6: _8696 = tmp_4
    结果类型: half
    操作数类型: half

==================================================
第1087行运算过程: half _16335 = half(-1.023326873779296875);
运算过程详情:
  步骤1: tmp_0 = -1.023326873779296875
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: _16335 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第1088行运算过程: half _16336 = half(1.023326873779296875);
运算过程详情:
  步骤1: tmp_0 = half(1.023326873779296875)
    结果类型: half
    操作数类型: float

  步骤2: _16336 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1089行运算过程: half _16342 = _9064.y;
运算过程详情:
  步骤1: tmp_0 = _9064.y
    结果类型: half
    操作数类型: half

  步骤2: _16342 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1090行运算过程: half _16351 = half(-0.858085691928863525390625);
运算过程详情:
  步骤1: tmp_0 = -0.858085691928863525390625
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: _16351 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第1091行运算过程: half4 _16356 = half4(_16351, half(0.7431240081787109375), _16351, half(0.4290428459644317626953125));
运算过程详情:
  步骤1: tmp_0 = half(0.7431240081787109375)
    结果类型: half
    操作数类型: float

  步骤2: tmp_1 = half(0.4290428459644317626953125)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = half4(_16351, tmp_0, _16351, tmp_1)
    结果类型: half4
    操作数类型: half, half, half, half

  步骤4: _16356 = tmp_2
    结果类型: half4
    操作数类型: half4

==================================================
第1092行运算过程: half _16361 = _9064.z;
运算过程详情:
  步骤1: tmp_0 = _9064.z
    结果类型: half
    操作数类型: half

  步骤2: _16361 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1093行运算过程: half _16369 = _9064.x;
运算过程详情:
  步骤1: tmp_0 = _9064.x
    结果类型: half
    操作数类型: half

  步骤2: _16369 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1094行运算过程: half4 _16385 = _16356 * half4(_16342 * _16361, _16361 * _16361, _16369 * _16361, (_16369 * _16369) - (_16342 * _16342));
运算过程详情:
  步骤1: tmp_0 = _16342 * _16361
    结果类型: half
    操作数类型: half, half

  步骤2: tmp_1 = _16361 * _16361
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = _16369 * _16361
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = _16369 * _16369
    结果类型: half
    操作数类型: half, half

  步骤5: tmp_4 = _16342 * _16342
    结果类型: half
    操作数类型: half, half

  步骤6: tmp_5 = tmp_3 - tmp_4
    结果类型: half
    操作数类型: half, half

  步骤7: tmp_6 = half4(tmp_0, tmp_1, tmp_2, tmp_5)
    结果类型: half4
    操作数类型: half, half, half, half

  步骤8: tmp_7 = _16356 * tmp_6
    结果类型: half4
    操作数类型: half4, half4

  步骤9: _16385 = tmp_7
    结果类型: half4
    操作数类型: half4

==================================================
第1095行运算过程: half _16387 = half(-0.2477079927921295166015625);
运算过程详情:
  步骤1: tmp_0 = -0.2477079927921295166015625
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = half(tmp_0)
    结果类型: half
    操作数类型: float

  步骤3: _16387 = tmp_1
    结果类型: half
    操作数类型: half

==================================================
第1096行运算过程: _16385.y = _16385.y + _16387;
运算过程详情:
  步骤1: tmp_0 = _16385.y
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 + _16387
    结果类型: half
    操作数类型: half, half

  步骤3: _16385.y = tmp_1
    操作数类型: half

==================================================
第1097行运算过程: half3 _16279 = half3(_Block1.cSHCoefficients[0].xyz * float3(half3(half(0.886226952075958251953125))));
运算过程详情:
  步骤1: tmp_0 = _Block1.cSHCoefficients
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = half3(tmp_0)
    结果类型: half3
    操作数类型: float4

  步骤3: _16279 = tmp_1
    结果类型: half3
    操作数类型: half3

==================================================
第1098行运算过程: float4 _16284 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16342))) * _9064.yzxx);
运算过程详情:
  步骤1: tmp_0 = float(_16342)
    结果类型: float
    操作数类型: half

  步骤2: tmp_1 = 0.858085691928863525390625 * tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half4(_16335, _16336, _16335, tmp_2)
    结果类型: half4
    操作数类型: half, half, half, half

  步骤5: tmp_4 = _9064.yzxx
    结果类型: unknown
    操作数类型: unknown

  步骤6: tmp_5 = tmp_3 * tmp_4
    结果类型: half4
    操作数类型: half4, unknown

  步骤7: tmp_6 = float4(tmp_5)
    结果类型: float4
    操作数类型: half4

  步骤8: _16284 = tmp_6
    结果类型: float4
    操作数类型: float4

==================================================
第1099行运算过程: float4 _16306 = float4(_16385);
运算过程详情:
  步骤1: tmp_0 = float4(_16385)
    结果类型: float4
    操作数类型: half4

  步骤2: _16306 = tmp_0
    结果类型: float4
    操作数类型: float4

==================================================
第1100行运算过程: half3 _16397 = half3(float3(0.081409998238086700439453125, 0.74361002445220947265625, -0.66364002227783203125));
运算过程详情:
  步骤1: tmp_0 = -0.66364002227783203125
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = float3(0.081409998238086700439453125, 0.74361002445220947265625, tmp_0)
    结果类型: float3
    操作数类型: float, float, float

  步骤3: tmp_2 = half3(tmp_1)
    结果类型: half3
    操作数类型: float3

  步骤4: _16397 = tmp_2
    结果类型: half3
    操作数类型: half3

==================================================
第1101行运算过程: half _16509 = _16397.y;
运算过程详情:
  步骤1: tmp_0 = _16397.y
    结果类型: half
    操作数类型: half

  步骤2: _16509 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1102行运算过程: half _16528 = _16397.z;
运算过程详情:
  步骤1: tmp_0 = _16397.z
    结果类型: half
    操作数类型: half

  步骤2: _16528 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1103行运算过程: half _16536 = _16397.x;
运算过程详情:
  步骤1: tmp_0 = _16397.x
    结果类型: half
    操作数类型: half

  步骤2: _16536 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1104行运算过程: half4 _16552 = _16356 * half4(_16509 * _16528, _16528 * _16528, _16536 * _16528, (_16536 * _16536) - (_16509 * _16509));
运算过程详情:
  步骤1: tmp_0 = _16509 * _16528
    结果类型: half
    操作数类型: half, half

  步骤2: tmp_1 = _16528 * _16528
    结果类型: half
    操作数类型: half, half

  步骤3: tmp_2 = _16536 * _16528
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = _16536 * _16536
    结果类型: half
    操作数类型: half, half

  步骤5: tmp_4 = _16509 * _16509
    结果类型: half
    操作数类型: half, half

  步骤6: tmp_5 = tmp_3 - tmp_4
    结果类型: half
    操作数类型: half, half

  步骤7: tmp_6 = half4(tmp_0, tmp_1, tmp_2, tmp_5)
    结果类型: half4
    操作数类型: half, half, half, half

  步骤8: tmp_7 = _16356 * tmp_6
    结果类型: half4
    操作数类型: half4, half4

  步骤9: _16552 = tmp_7
    结果类型: half4
    操作数类型: half4

==================================================
第1105行运算过程: _16552.y = _16552.y + _16387;
运算过程详情:
  步骤1: tmp_0 = _16552.y
    结果类型: half
    操作数类型: half

  步骤2: tmp_1 = tmp_0 + _16387
    结果类型: half
    操作数类型: half, half

  步骤3: _16552.y = tmp_1
    操作数类型: half

==================================================
第1106行运算过程: float4 _16451 = float4(half4(_16335, _16336, _16335, half(0.858085691928863525390625 * float(_16509))) * _16397.yzxx);
运算过程详情:
  步骤1: tmp_0 = float(_16509)
    结果类型: float
    操作数类型: half

  步骤2: tmp_1 = 0.858085691928863525390625 * tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half4(_16335, _16336, _16335, tmp_2)
    结果类型: half4
    操作数类型: half, half, half, half

  步骤5: tmp_4 = _16397.yzxx
    结果类型: unknown
    操作数类型: unknown

  步骤6: tmp_5 = tmp_3 * tmp_4
    结果类型: half4
    操作数类型: half4, unknown

  步骤7: tmp_6 = float4(tmp_5)
    结果类型: float4
    操作数类型: half4

  步骤8: _16451 = tmp_6
    结果类型: float4
    操作数类型: float4

==================================================
第1107行运算过程: float4 _16473 = float4(_16552);
运算过程详情:
  步骤1: tmp_0 = float4(_16552)
    结果类型: float4
    操作数类型: half4

  步骤2: _16473 = tmp_0
    结果类型: float4
    操作数类型: float4

==================================================
第1108行运算过程: half3 _16258 = ((((max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16284)), half(dot(_Block1.cSHCoefficients[3], _16284)), half(dot(_Block1.cSHCoefficients[5], _16284))) + half3(half(dot(_Block1.cSHCoefficients[2], _16306)), half(dot(_Block1.cSHCoefficients[4], _16306)), half(dot(_Block1.cSHCoefficients[6], _16306)))), _9179) * half3(half(0.699999988079071044921875 + (0.300000011920928955078125 * _Block1.EnvInfo.z)))) * half3(half(float(_8295 * _18329) * mix(1.0, _Block1.WorldProbeInfo.y, step(0.0, _Block1.cVisibilitySH[0].w))))) * half3(half(_Block1.GIInfo.z))) + half3(float3(max(_16279 + (half3(half(dot(_Block1.cSHCoefficients[1], _16451)), half(dot(_Block1.cSHCoefficients[3], _16451)), half(dot(_Block1.cSHCoefficients[5], _16451))) + half3(half(dot(_Block1.cSHCoefficients[2], _16473)), half(dot(_Block1.cSHCoefficients[4], _16473)), half(dot(_Block1.cSHCoefficients[6], _16473)))), _9179)) * float3(((3.1415927410125732421875 * float(clamp(dot(_9064, _16397), half(0.0), half(1.0)))) * _Block1.WorldProbeInfo.w) * float(half(float(_8295 * _18236)) * half(0.5))))) * half3(half(_Block1.cSHCoefficients[0].w));
运算过程详情:
  步骤1: tmp_0 = _Block1.cSHCoefficients
    结果类型: float4
    操作数类型: float4

  步骤2: tmp_1 = dot(tmp_0)
    结果类型: float
    操作数类型: float4

  步骤3: tmp_2 = half(tmp_1)
    结果类型: half
    操作数类型: float

  步骤4: tmp_3 = half3(tmp_2)
    结果类型: half3
    操作数类型: half

  步骤5: tmp_4 = _16279 + tmp_3
    结果类型: half3
    操作数类型: half3, half3

  步骤6: tmp_5 = max(tmp_4)
    结果类型: float
    操作数类型: half3

  步骤7: _16258 = tmp_5
    结果类型: half3
    操作数类型: float

==================================================
第1109行运算过程: half3 _8776 = ((_18255 + (half3((float3(_15813 * (half(fast::min(1000.0, (_15839 * _15839) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * _15861, _12108)))) * _Block1.cVirtualLitColor.xyz) * _15805) + half3((_Block1.cVirtualLitColor.xyz * abs(_8378)) * _15762))) + half3(fast::min(float3(8192.0), ((((float3((_15813 * (half(fast::min(1000.0, (_16159 * _16159) * 0.3183098733425140380859375)) * half(0.25 / fast::max(_15861 * ((float(_16031) * _15858) + _15854), _12108)))) * _16031) * float3(_Block1.cLocalVirtualLitPos.w)) + float3(_10569 * _16031)) * float(half(fast::min(float(half((_15986 * _15986) / ((float(_15974) * abs(_Block1.cLocalVirtualLitCustom.y)) + 9.9999997473787516355514526367188e-05))), _Block1.cLocalVirtualLitCustom.w)))) * (_Block1.cLocalVirtualLitColor.xyz * abs(_Block1.cLocalVirtualLitColor.w))) * _Block1.DiyLightingInfo.z))) * _8696;
运算过程详情:
  步骤1: tmp_0 = _15839 * _15839
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = tmp_0 * 0.3183098733425140380859375
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = fast::min(1000.0, tmp_1)
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = half(tmp_2)
    结果类型: half
    操作数类型: float

  步骤5: tmp_4 = _15861 * _15861
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::max(tmp_4, _12108)
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = 0.25 / tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = half(tmp_6)
    结果类型: half
    操作数类型: float

  步骤9: tmp_8 = tmp_3 * tmp_7
    结果类型: half
    操作数类型: half, half

  步骤10: tmp_9 = _15813 * tmp_8
    结果类型: half
    操作数类型: half3, half

  步骤11: tmp_10 = float3(tmp_9)
    结果类型: float3
    操作数类型: half

  步骤12: tmp_11 = _Block1.cVirtualLitColor.xyz
    结果类型: float3
    操作数类型: float3

  步骤13: tmp_12 = tmp_10 * tmp_11
    结果类型: float3
    操作数类型: float3, float3

  步骤14: tmp_13 = tmp_12 * _15805
    结果类型: float
    操作数类型: float3, float

  步骤15: tmp_14 = half3(tmp_13)
    结果类型: half3
    操作数类型: float

  步骤16: tmp_15 = _Block1.cVirtualLitColor.xyz
    结果类型: float3
    操作数类型: float3

  步骤17: tmp_16 = abs(_8378)
    结果类型: float
    操作数类型: float

  步骤18: tmp_17 = tmp_15 * tmp_16
    结果类型: float
    操作数类型: float3, float

  步骤19: tmp_18 = tmp_17 * _15762
    结果类型: float
    操作数类型: float, float3

  步骤20: tmp_19 = half3(tmp_18)
    结果类型: half3
    操作数类型: float

  步骤21: tmp_20 = tmp_14 + tmp_19
    结果类型: half3
    操作数类型: half3, half3

  步骤22: tmp_21 = _18255 + tmp_20
    结果类型: half3
    操作数类型: unknown, half3

  步骤23: tmp_22 = float3(8192.0)
    结果类型: float3
    操作数类型: float

  步骤24: tmp_23 = _16159 * _16159
    结果类型: float
    操作数类型: float, float

  步骤25: tmp_24 = tmp_23 * 0.3183098733425140380859375
    结果类型: float
    操作数类型: float, float

  步骤26: tmp_25 = fast::min(1000.0, tmp_24)
    结果类型: float
    操作数类型: float, float

  步骤27: tmp_26 = half(tmp_25)
    结果类型: half
    操作数类型: float

  步骤28: tmp_27 = float(_16031)
    结果类型: float
    操作数类型: half

  步骤29: tmp_28 = tmp_27 * _15858
    结果类型: float
    操作数类型: float, float

  步骤30: tmp_29 = tmp_28 + _15854
    结果类型: float
    操作数类型: float, float

  步骤31: tmp_30 = _15861 * tmp_29
    结果类型: float
    操作数类型: float, float

  步骤32: tmp_31 = fast::max(tmp_30, _12108)
    结果类型: float
    操作数类型: float, float

  步骤33: tmp_32 = 0.25 / tmp_31
    结果类型: float
    操作数类型: float, float

  步骤34: tmp_33 = half(tmp_32)
    结果类型: half
    操作数类型: float

  步骤35: tmp_34 = tmp_26 * tmp_33
    结果类型: half
    操作数类型: half, half

  步骤36: tmp_35 = _15813 * tmp_34
    结果类型: half
    操作数类型: half3, half

  步骤37: tmp_36 = tmp_35 * _16031
    结果类型: half
    操作数类型: half, half

  步骤38: tmp_37 = float3(tmp_36)
    结果类型: float3
    操作数类型: half

  步骤39: tmp_38 = _Block1.cLocalVirtualLitPos.w
    结果类型: float
    操作数类型: float

  步骤40: tmp_39 = float3(tmp_38)
    结果类型: float3
    操作数类型: float

  步骤41: tmp_40 = tmp_37 * tmp_39
    结果类型: float3
    操作数类型: float3, float3

  步骤42: tmp_41 = _10569 * _16031
    结果类型: half
    操作数类型: half3, half

  步骤43: tmp_42 = float3(tmp_41)
    结果类型: float3
    操作数类型: half

  步骤44: tmp_43 = tmp_40 + tmp_42
    结果类型: float3
    操作数类型: float3, float3

  步骤45: tmp_44 = _15986 * _15986
    结果类型: float
    操作数类型: float, float

  步骤46: tmp_45 = float(_15974)
    结果类型: float
    操作数类型: half

  步骤47: tmp_46 = _Block1.cLocalVirtualLitCustom.y
    结果类型: float
    操作数类型: float

  步骤48: tmp_47 = abs(tmp_46)
    结果类型: float
    操作数类型: float

  步骤49: tmp_48 = tmp_45 * tmp_47
    结果类型: float
    操作数类型: float, float

  步骤50: tmp_49 = tmp_48 + 9.9999997473787516355514526367188
    结果类型: float
    操作数类型: float, float

  步骤51: tmp_50 = tmp_44 / tmp_49
    结果类型: float
    操作数类型: float, float

  步骤52: tmp_51 = half(tmp_50)
    结果类型: half
    操作数类型: float

  步骤53: tmp_52 = float(tmp_51)
    结果类型: float
    操作数类型: half

  步骤54: tmp_53 = fast::min(tmp_52)
    结果类型: float
    操作数类型: float

  步骤55: tmp_54 = half(tmp_53)
    结果类型: half
    操作数类型: float

  步骤56: tmp_55 = float(tmp_54)
    结果类型: float
    操作数类型: half

  步骤57: tmp_56 = tmp_43 * tmp_55
    结果类型: float
    操作数类型: float3, float

  步骤58: tmp_57 = fast::min(tmp_22, tmp_56)
    结果类型: float3
    操作数类型: float3, float

  步骤59: tmp_58 = half3(tmp_57)
    结果类型: half3
    操作数类型: float3

  步骤60: tmp_59 = tmp_21 + tmp_58
    结果类型: half3
    操作数类型: half3, half3

  步骤61: _8776 = tmp_59
    结果类型: half3
    操作数类型: half3

==================================================
第1110行运算过程: float _16622 = length(_8370);
运算过程详情:
  步骤1: tmp_0 = length(_8370)
    结果类型: float
    操作数类型: float3

  步骤2: _16622 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1111行运算过程: float _16645 = _8370.y;
运算过程详情:
  步骤1: tmp_0 = _8370.y
    结果类型: float
    操作数类型: float

  步骤2: _16645 = tmp_0
    结果类型: float
    操作数类型: float

==================================================
第1112行运算过程: float _16657 = (_16645 + _12108) + float((_9251 * half(9.9956989288330078125e-05)) * half(int(sign(_16645))));
运算过程详情:
  步骤1: tmp_0 = _16645 + _12108
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = half(9.9956989288330078125)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = _9251 * tmp_1
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = float(tmp_2)
    结果类型: float
    操作数类型: half

  步骤5: tmp_4 = tmp_0 + tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: _16657 = tmp_4
    结果类型: float
    操作数类型: float

==================================================
第1113行运算过程: float2 _16682 = fast::max(float2(0.0), float2(_Block1.FogInfo.w, exp((-_Block1.AerialPerspectiveMie.y) * _Block1.CameraPos.y) * _Block1.AerialPerspectiveMie.z) * ((float2(1.0) - exp(-fast::min(float2(_Block1.FogInfo.z, _Block1.AerialPerspectiveMie.y) * _16657, float2(10.0)))) / float2(_16657)));
运算过程详情:
  步骤1: tmp_0 = float2(0.0)
    结果类型: float2
    操作数类型: float

  步骤2: tmp_1 = _Block1.FogInfo.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _Block1.AerialPerspectiveMie.y
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = -tmp_2
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _Block1.CameraPos.y
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = tmp_3 * tmp_4
    结果类型: float
    操作数类型: float, float

  步骤7: tmp_6 = exp(tmp_5)
    结果类型: float
    操作数类型: float

  步骤8: tmp_7 = _Block1.AerialPerspectiveMie.z
    结果类型: float
    操作数类型: float

  步骤9: tmp_8 = tmp_6 * tmp_7
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = float2(tmp_1, tmp_8)
    结果类型: float2
    操作数类型: float, float

  步骤11: tmp_10 = float2(1.0)
    结果类型: float2
    操作数类型: float

  步骤12: tmp_11 = _Block1.FogInfo.z
    结果类型: float
    操作数类型: float

  步骤13: tmp_12 = _Block1.AerialPerspectiveMie.y
    结果类型: float
    操作数类型: float

  步骤14: tmp_13 = float2(tmp_11, tmp_12)
    结果类型: float2
    操作数类型: float, float

  步骤15: tmp_14 = tmp_13 * _16657
    结果类型: float
    操作数类型: float2, float

  步骤16: tmp_15 = float2(10.0)
    结果类型: float2
    操作数类型: float

  步骤17: tmp_16 = fast::min(tmp_14, tmp_15)
    结果类型: float2
    操作数类型: float, float2

  步骤18: tmp_17 = -tmp_16
    结果类型: float2
    操作数类型: float2

  步骤19: tmp_18 = exp(tmp_17)
    结果类型: float2
    操作数类型: float2

  步骤20: tmp_19 = tmp_10 - tmp_18
    结果类型: float2
    操作数类型: float2, float2

  步骤21: tmp_20 = float2(_16657)
    结果类型: float2
    操作数类型: float

  步骤22: tmp_21 = tmp_19 / tmp_20
    结果类型: float2
    操作数类型: float2, float2

  步骤23: tmp_22 = tmp_9 * tmp_21
    结果类型: float2
    操作数类型: float2, float2

  步骤24: tmp_23 = fast::max(tmp_0, tmp_22)
    结果类型: float2
    操作数类型: float2, float2

  步骤25: _16682 = tmp_23
    结果类型: float2
    操作数类型: float2

==================================================
第1114行运算过程: float3 _16688 = fast::max(float3(_12108), _Block1.AerialPerspectiveExt.xyz);
运算过程详情:
  步骤1: tmp_0 = float3(_12108)
    结果类型: float3
    操作数类型: float

  步骤2: tmp_1 = _Block1.AerialPerspectiveExt.xyz
    结果类型: float3
    操作数类型: float3

  步骤3: tmp_2 = fast::max(tmp_0, tmp_1)
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _16688 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第1115行运算过程: float3 _16698 = float3(_8303);
运算过程详情:
  步骤1: tmp_0 = float3(_8303)
    结果类型: float3
    操作数类型: half3

  步骤2: _16698 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第1116行运算过程: float3 _16715 = exp(-(_16688 * ((_16622 * (_Block1.FogColor.w + ((1.0 - _Block1.FogColor.w) * fast::clamp(_16622 / _Block1.FogInfo.x, 0.0, 1.0)))) * ((_16682.x / dot(_16688, _16698)) + ((_16682.y * fast::max(9.9999999747524270787835121154785e-07, _Block1.AerialPerspectiveRay.w * 0.0005000000237487256526947021484375)) * 5.0)))));
运算过程详情:
  步骤1: tmp_0 = _Block1.FogColor.w
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.FogColor.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = 1.0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = _Block1.FogInfo.x
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _16622 / tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = fast::clamp(tmp_4, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤7: tmp_6 = tmp_2 * tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_0 + tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = _16622 * tmp_7
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = _16682.x
    结果类型: float
    操作数类型: float

  步骤11: tmp_10 = dot(_16688, _16698)
    结果类型: float
    操作数类型: float3, float3

  步骤12: tmp_11 = tmp_9 / tmp_10
    结果类型: float
    操作数类型: float, float

  步骤13: tmp_12 = _16682.y
    结果类型: float
    操作数类型: float

  步骤14: tmp_13 = fast::max(9.9999999747524270787835121154785)
    结果类型: float
    操作数类型: float

  步骤15: tmp_14 = tmp_12 * tmp_13
    结果类型: float
    操作数类型: float, float

  步骤16: tmp_15 = tmp_11 + tmp_14
    结果类型: float
    操作数类型: float, float

  步骤17: tmp_16 = tmp_8 * tmp_15
    结果类型: float
    操作数类型: float, float

  步骤18: tmp_17 = _16688 * tmp_16
    结果类型: float
    操作数类型: float3, float

  步骤19: tmp_18 = -tmp_17
    结果类型: float
    操作数类型: float

  步骤20: tmp_19 = exp(tmp_18)
    结果类型: float
    操作数类型: float

  步骤21: _16715 = tmp_19
    结果类型: float3
    操作数类型: float

==================================================
第1117行运算过程: float3 _16602 = fast::normalize(_8370);
运算过程详情:
  步骤1: tmp_0 = fast::normalize(_8370)
    结果类型: float3
    操作数类型: float3

  步骤2: _16602 = tmp_0
    结果类型: float3
    操作数类型: float3

==================================================
第1118行运算过程: float _16756 = fast::clamp(dot(_16602, _Block1.OriginSunDir.xyz), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.OriginSunDir.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = dot(_16602, tmp_0)
    结果类型: float
    操作数类型: float3, float3

  步骤3: tmp_2 = fast::clamp(tmp_1, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤4: _16756 = tmp_2
    结果类型: float
    操作数类型: float

==================================================
第1119行运算过程: float _16759 = fast::max(0.0, _16602.y);
运算过程详情:
  步骤1: tmp_0 = _16602.y
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = fast::max(0.0, tmp_0)
    结果类型: float
    操作数类型: float, float

  步骤3: _16759 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第1120行运算过程: float _16820 = fast::clamp((_16622 - 80.0) / fast::max(_12108, 520.0), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _16622 - 80.0
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = fast::max(_12108, 520.0)
    结果类型: float
    操作数类型: float, float

  步骤3: tmp_2 = tmp_0 / tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = fast::clamp(tmp_2, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤5: _16820 = tmp_3
    结果类型: float
    操作数类型: float

==================================================
第1121行运算过程: float _16778 = 1.0 - (_16759 * _16759);
运算过程详情:
  步骤1: tmp_0 = _16759 * _16759
    结果类型: float
    操作数类型: float, float

  步骤2: tmp_1 = 1.0 - tmp_0
    结果类型: float
    操作数类型: float, float

  步骤3: _16778 = tmp_1
    结果类型: float
    操作数类型: float

==================================================
第1122行运算过程: float3 _16785 = float3(((1.0 - (_Block1.AerialPerspectiveExt.w * _Block1.AerialPerspectiveExt.w)) / (12.56637096405029296875 * powr(fast::max(1.0 + (_Block1.AerialPerspectiveExt.w * (_Block1.AerialPerspectiveExt.w - (2.0 * _16756))), _12108), 1.5))) * (_16820 * _16820)) * _Block1.SunFogColor.xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.AerialPerspectiveExt.w
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.AerialPerspectiveExt.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 * tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = 1.0 - tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = _Block1.AerialPerspectiveExt.w
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = _Block1.AerialPerspectiveExt.w
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = 2.0 * _16756
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = tmp_5 - tmp_6
    结果类型: float
    操作数类型: float, float

  步骤9: tmp_8 = tmp_4 * tmp_7
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = 1.0 + tmp_8
    结果类型: float
    操作数类型: float, float

  步骤11: tmp_10 = fast::max(tmp_9, _12108)
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = powr(tmp_10, 1.5)
    结果类型: float
    操作数类型: float, float

  步骤13: tmp_12 = 12.56637096405029296875 * tmp_11
    结果类型: float
    操作数类型: float, float

  步骤14: tmp_13 = tmp_3 / tmp_12
    结果类型: float
    操作数类型: float, float

  步骤15: tmp_14 = _16820 * _16820
    结果类型: float
    操作数类型: float, float

  步骤16: tmp_15 = tmp_13 * tmp_14
    结果类型: float
    操作数类型: float, float

  步骤17: tmp_16 = float3(tmp_15)
    结果类型: float3
    操作数类型: float

  步骤18: tmp_17 = _Block1.SunFogColor.xyz
    结果类型: float3
    操作数类型: float3

  步骤19: tmp_18 = tmp_16 * tmp_17
    结果类型: float3
    操作数类型: float3, float3

  步骤20: _16785 = tmp_18
    结果类型: float3
    操作数类型: float3

==================================================
第1123行运算过程: half _16793 = half(_16756);
运算过程详情:
  步骤1: tmp_0 = half(_16756)
    结果类型: half
    操作数类型: float

  步骤2: _16793 = tmp_0
    结果类型: half
    操作数类型: half

==================================================
第1124行运算过程: float3 _16805 = ((_Block1.AerialPerspectiveRay.xyz * float(half(1.0) + (_16793 * _16793))) + (_16785 * _Block1.AerialPerspectiveMie.x)) + ((_Block1.FogColor.xyz * (0.0596831031143665313720703125 * (1.0 + (_16778 * _16778)))) + _16785);
运算过程详情:
  步骤1: tmp_0 = _Block1.AerialPerspectiveRay.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = half(1.0)
    结果类型: half
    操作数类型: float

  步骤3: tmp_2 = _16793 * _16793
    结果类型: half
    操作数类型: half, half

  步骤4: tmp_3 = tmp_1 + tmp_2
    结果类型: half
    操作数类型: half, half

  步骤5: tmp_4 = float(tmp_3)
    结果类型: float
    操作数类型: half

  步骤6: tmp_5 = tmp_0 * tmp_4
    结果类型: float
    操作数类型: float3, float

  步骤7: tmp_6 = _Block1.AerialPerspectiveMie.x
    结果类型: float
    操作数类型: float

  步骤8: tmp_7 = _16785 * tmp_6
    结果类型: float
    操作数类型: float3, float

  步骤9: tmp_8 = tmp_5 + tmp_7
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = _Block1.FogColor.xyz
    结果类型: float3
    操作数类型: float3

  步骤11: tmp_10 = _16778 * _16778
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = 1.0 + tmp_10
    结果类型: float
    操作数类型: float, float

  步骤13: tmp_12 = 0.0596831031143665313720703125 * tmp_11
    结果类型: float
    操作数类型: float, float

  步骤14: tmp_13 = tmp_9 * tmp_12
    结果类型: float
    操作数类型: float3, float

  步骤15: tmp_14 = tmp_13 + _16785
    结果类型: float
    操作数类型: float, float3

  步骤16: tmp_15 = tmp_8 + tmp_14
    结果类型: float
    操作数类型: float, float

  步骤17: _16805 = tmp_15
    结果类型: float3
    操作数类型: float

==================================================
第1125行运算过程: float3 _16862 = (((((((((((_Block1.cVisibilitySH[0].xyz * _8657) * _15762) * _Block1.AmbientColor.w) * _Block1.ReflectionProbeBBMin.w) * float3(_Block1.cSHCoefficients[0].w)).xyz + float3(_18268)).xyz + (_18989 + float3(((_9179 * _9254) + _8393) * _8696))).xyz + float3((_19028 + half3(float3(_16258) * float3((_15805 * 0.5) + 0.5))) * _10569)).xyz + float3((half3(_9698 * _10588) * _11772) + _8776)).xyz * dot(_16715, _16698)) + (_16805 - (_16805 * _16715))).xyz;
运算过程详情:
  步骤1: tmp_0 = _Block1.cVisibilitySH
    结果类型: float4
    操作数类型: float4

  步骤2: _16862 = tmp_0
    结果类型: float3
    操作数类型: float4

==================================================
第1126行运算过程: float3 _8804 = (_16862 * _9868).xyz;
运算过程详情:
  步骤1: tmp_0 = _16862 * _9868
    结果类型: float
    操作数类型: float3, float

  步骤2: _8804 = tmp_0
    结果类型: float3
    操作数类型: float

==================================================
第1128行运算过程: if (_Block1.eIsPlayerOverride < 0.5)
  无运算过程
==================================================
第1131行运算过程: if ((_Block1.ScreenMotionGray.x * _Block1.ScreenMotionGray.x) > _12108)
  无运算过程
==================================================
第1133行运算过程: float _16911 = fast::clamp((_Block1.CameraPos.w - _Block1.ScreenMotionGray.w) / fast::max(_12108, (_Block1.ScreenMotionGray.w + abs(_Block1.ScreenMotionGray.x)) - _Block1.ScreenMotionGray.w), 0.0, 1.0);
运算过程详情:
  步骤1: tmp_0 = _Block1.CameraPos.w
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _Block1.ScreenMotionGray.w
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = tmp_0 - tmp_1
    结果类型: float
    操作数类型: float, float

  步骤4: tmp_3 = _Block1.ScreenMotionGray.w
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = _Block1.ScreenMotionGray.x
    结果类型: float
    操作数类型: float

  步骤6: tmp_5 = abs(tmp_4)
    结果类型: float
    操作数类型: float

  步骤7: tmp_6 = tmp_3 + tmp_5
    结果类型: float
    操作数类型: float, float

  步骤8: tmp_7 = _Block1.ScreenMotionGray.w
    结果类型: float
    操作数类型: float

  步骤9: tmp_8 = tmp_6 - tmp_7
    结果类型: float
    操作数类型: float, float

  步骤10: tmp_9 = fast::max(_12108, tmp_8)
    结果类型: float
    操作数类型: float, float

  步骤11: tmp_10 = tmp_2 / tmp_9
    结果类型: float
    操作数类型: float, float

  步骤12: tmp_11 = fast::clamp(tmp_10, 0.0, 1.0)
    结果类型: float
    操作数类型: float, float, float

  步骤13: _16911 = tmp_11
    结果类型: float
    操作数类型: float

==================================================
第1135行运算过程: if (_Block1.ScreenMotionGray.x > 0.001000000047497451305389404296875)
  无运算过程
==================================================
第1137行运算过程: _19029 = 1.0 - _16911;
运算过程详情:
  步骤1: tmp_0 = 1.0 - _16911
    结果类型: float
    操作数类型: float, float

  步骤2: _19029 = tmp_0
    操作数类型: float

==================================================
第1141行运算过程: _19029 = _16911;
运算过程详情:
  步骤1: _19029 = _16911
    操作数类型: float

==================================================
第1143行运算过程: _19032 = mix(_8804, float3(dot(_8804, _16698) * (0.00999999977648258209228515625 * floor(_Block1.ScreenMotionGray.z))), float3(_19029 * fract(_Block1.ScreenMotionGray.z)));
运算过程详情:
  步骤1: tmp_0 = dot(_8804, _16698)
    结果类型: float
    操作数类型: float3, float3

  步骤2: tmp_1 = _Block1.ScreenMotionGray.z
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = floor(tmp_1)
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = 0.00999999977648258209228515625 * tmp_2
    结果类型: float
    操作数类型: float, float

  步骤5: tmp_4 = tmp_0 * tmp_3
    结果类型: float
    操作数类型: float, float

  步骤6: tmp_5 = float3(tmp_4)
    结果类型: float3
    操作数类型: float

  步骤7: tmp_6 = _Block1.ScreenMotionGray.z
    结果类型: float
    操作数类型: float

  步骤8: tmp_7 = fract(tmp_6)
    结果类型: float
    操作数类型: float

  步骤9: tmp_8 = _19029 * tmp_7
    结果类型: float
    操作数类型: unknown, float

  步骤10: tmp_9 = float3(tmp_8)
    结果类型: float3
    操作数类型: float

  步骤11: tmp_10 = mix(_8804, tmp_5, tmp_9)
    结果类型: float3
    操作数类型: float3, float3, float3

  步骤12: _19032 = tmp_10
    操作数类型: float3

==================================================
第1147行运算过程: _19032 = _8804;
运算过程详情:
  步骤1: _19032 = _8804
    操作数类型: float3

==================================================
第1149行运算过程: _19031 = _19032;
运算过程详情:
  步骤1: _19031 = _19032
    操作数类型: unknown

==================================================
第1153行运算过程: _19031 = _8804;
运算过程详情:
  步骤1: _19031 = _8804
    操作数类型: float3

==================================================
第1155行运算过程: float4 _8808 = float4(_19031.x, _19031.y, _19031.z, float4(0.0).w);
运算过程详情:
  步骤1: tmp_0 = _19031.x
    结果类型: unknown
    操作数类型: unknown

  步骤2: tmp_1 = _19031.y
    结果类型: unknown
    操作数类型: unknown

  步骤3: tmp_2 = _19031.z
    结果类型: unknown
    操作数类型: unknown

  步骤4: tmp_3 = float4(0.0)
    结果类型: float4
    操作数类型: float

  步骤5: tmp_4 = float4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: float4
    操作数类型: unknown, unknown, unknown, float4

  步骤6: _8808 = tmp_4
    结果类型: float4
    操作数类型: float4

==================================================
第1156行运算过程: _8808.w = _9868;
运算过程详情:
  步骤1: _8808.w = _9868
    操作数类型: float

==================================================
第1157行运算过程: float3 _8816 = fast::min(_8808.xyz, float3(10000.0));
运算过程详情:
  步骤1: tmp_0 = _8808.xyz
    结果类型: float3
    操作数类型: float3

  步骤2: tmp_1 = float3(10000.0)
    结果类型: float3
    操作数类型: float

  步骤3: tmp_2 = fast::min(tmp_0, tmp_1)
    结果类型: float3
    操作数类型: float3, float3

  步骤4: _8816 = tmp_2
    结果类型: float3
    操作数类型: float3

==================================================
第1158行运算过程: out._Ret = float4(_8816.x, _8816.y, _8816.z, _8808.w);
运算过程详情:
  步骤1: tmp_0 = _8816.x
    结果类型: float
    操作数类型: float

  步骤2: tmp_1 = _8816.y
    结果类型: float
    操作数类型: float

  步骤3: tmp_2 = _8816.z
    结果类型: float
    操作数类型: float

  步骤4: tmp_3 = _8808.w
    结果类型: float
    操作数类型: float

  步骤5: tmp_4 = float4(tmp_0, tmp_1, tmp_2, tmp_3)
    结果类型: float4
    操作数类型: float, float, float, float

  步骤6: out._Ret = tmp_4
    操作数类型: float4

==================================================
第1159行运算过程: return out;
  无运算过程
==================================================
