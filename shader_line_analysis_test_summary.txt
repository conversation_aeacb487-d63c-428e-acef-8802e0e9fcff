🎯 语法树分析摘要报告
==================================================
📊 基本统计:
  总代码行: 762
  变量声明: 1004
  运算过程: 2097
  临时变量: 1628

🔍 类型分析:
  类型转换: 572
  精度问题: 0

🎨 类型分布:
  bool: 9 (0.9%)
  float: 395 (39.3%)
  float2: 62 (6.2%)
  float3: 236 (23.5%)
  float3x3: 5 (0.5%)
  float4: 20 (2.0%)
  half: 127 (12.6%)
  half3: 57 (5.7%)
  half4: 32 (3.2%)
  int: 4 (0.4%)
  uint: 57 (5.7%)

📈 语法树分析特点:
  基于AST的精确语法解析
  完整的运算过程分解
  临时变量类型推断
  向量成员访问支持

💡 性能建议:
  ⚠️  发现 572 个类型转换，建议优化