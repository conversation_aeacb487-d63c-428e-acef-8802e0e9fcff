🎯 语法树分析摘要报告
==================================================
📊 基本统计:
  总代码行: 762
  变量声明: 1143
  运算过程: 2730
  临时变量: 2031

🔍 类型分析:
  类型转换: 975
  精度问题: 0

🎨 类型分布:
  bool: 3 (0.3%)
  float: 437 (38.2%)
  float2: 62 (5.4%)
  float3: 281 (24.6%)
  float3x3: 5 (0.4%)
  float4: 21 (1.8%)
  half: 152 (13.3%)
  half3: 94 (8.2%)
  half4: 34 (3.0%)
  int: 5 (0.4%)
  uint: 48 (4.2%)
  unknown: 1 (0.1%)

📈 语法树分析特点:
  基于AST的精确语法解析
  完整的运算过程分解
  临时变量类型推断
  向量成员访问支持

💡 性能建议:
  ⚠️  发现 975 个类型转换，建议优化