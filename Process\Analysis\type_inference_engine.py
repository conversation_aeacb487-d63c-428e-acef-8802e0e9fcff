#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类型推断引擎 - 使用树形结构管理对象调用和方法调用的类型推断
"""

import re
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum

class DataType(Enum):
    """数据类型枚举"""
    UNKNOWN = "unknown"
    VOID = "void"
    BOOL = "bool"
    INT = "int"
    UINT = "uint"
    FLOAT = "float"
    HALF = "half"
    FLOAT2 = "float2"
    FLOAT3 = "float3"
    FLOAT4 = "float4"
    HALF2 = "half2"
    HALF3 = "half3"
    HALF4 = "half4"
    FLOAT3X3 = "float3x3"
    FLOAT4X4 = "float4x4"
    TEXTURE2D = "texture2d"
    SAMPLER = "sampler"

@dataclass
class TypeNode:
    """类型节点 - 树形结构的节点"""
    name: str
    data_type: DataType
    children: Dict[str, 'TypeNode']  # 子成员或方法
    methods: Dict[str, 'MethodInfo']  # 方法信息
    is_builtin: bool = False  # 是否是内置类型
    
    def __post_init__(self):
        if self.children is None:
            self.children = {}
        if self.methods is None:
            self.methods = {}

@dataclass
class MethodInfo:
    """方法信息"""
    name: str
    return_type: DataType
    parameters: List[DataType]
    description: str = ""

@dataclass
class FunctionInfo:
    """函数信息"""
    name: str
    return_type: DataType
    parameters: List[DataType]
    is_builtin: bool = False
    description: str = ""

class TypeInferenceEngine:
    """类型推断引擎"""
    
    def __init__(self):
        # 根节点存储所有类型定义
        self.type_registry: Dict[str, TypeNode] = {}
        # 变量类型映射
        self.variable_types: Dict[str, str] = {}
        # 函数定义存储
        self.function_registry: Dict[str, FunctionInfo] = {}

        # 初始化内置类型和函数
        self._initialize_builtin_types()
        self._initialize_builtin_functions()
    
    def _initialize_builtin_types(self):
        """初始化着色器内置类型"""
        
        # 标量类型
        self._register_scalar_types()
        
        # 向量类型
        self._register_vector_types()
        
        # 矩阵类型
        self._register_matrix_types()
        
        # 纹理和采样器类型
        self._register_texture_types()
    
    def _register_scalar_types(self):
        """注册标量类型"""
        scalar_types = [
            DataType.BOOL, DataType.INT, DataType.UINT, 
            DataType.FLOAT, DataType.HALF
        ]
        
        for scalar_type in scalar_types:
            node = TypeNode(
                name=scalar_type.value,
                data_type=scalar_type,
                children={},
                methods={},
                is_builtin=True
            )
            self.type_registry[scalar_type.value] = node
    
    def _register_vector_types(self):
        """注册向量类型及其成员访问"""
        
        # float2 类型
        float2_node = TypeNode(
            name="float2",
            data_type=DataType.FLOAT2,
            children={},
            methods={},
            is_builtin=True
        )
        
        # 添加向量成员访问
        self._add_vector_swizzles(float2_node, DataType.FLOAT, 2)
        self.type_registry["float2"] = float2_node
        
        # float3 类型
        float3_node = TypeNode(
            name="float3",
            data_type=DataType.FLOAT3,
            children={},
            methods={},
            is_builtin=True
        )
        self._add_vector_swizzles(float3_node, DataType.FLOAT, 3)
        self.type_registry["float3"] = float3_node
        
        # float4 类型
        float4_node = TypeNode(
            name="float4",
            data_type=DataType.FLOAT4,
            children={},
            methods={},
            is_builtin=True
        )
        self._add_vector_swizzles(float4_node, DataType.FLOAT, 4)
        self.type_registry["float4"] = float4_node
        
        # half 向量类型
        for size in [2, 3, 4]:
            half_type = getattr(DataType, f"HALF{size}")
            half_node = TypeNode(
                name=half_type.value,
                data_type=half_type,
                children={},
                methods={},
                is_builtin=True
            )
            self._add_vector_swizzles(half_node, DataType.HALF, size)
            self.type_registry[half_type.value] = half_node
    
    def _add_vector_swizzles(self, vector_node: TypeNode, component_type: DataType, size: int):
        """为向量类型添加成员访问（swizzle）"""
        components = ['x', 'y', 'z', 'w'][:size]
        color_components = ['r', 'g', 'b', 'a'][:size]
        
        # 单个分量访问
        for i, comp in enumerate(components):
            vector_node.children[comp] = TypeNode(comp, component_type, {}, {}, True)
            if i < len(color_components):
                vector_node.children[color_components[i]] = TypeNode(color_components[i], component_type, {}, {}, True)
        
        # 两个分量组合
        if size >= 2:
            result_type = DataType.HALF2 if component_type == DataType.HALF else DataType.FLOAT2
            two_comps = ['xy', 'xz', 'yz', 'rg', 'rb', 'gb']
            if size >= 3:
                two_comps.extend(['xw', 'yw', 'zw', 'ra', 'ga', 'ba'])
            if size >= 4:
                two_comps.extend(['yw', 'zw'])
            
            for comp in two_comps:
                if all(c in components + color_components for c in comp):
                    vector_node.children[comp] = TypeNode(comp, result_type, {}, {}, True)
        
        # 三个分量组合
        if size >= 3:
            result_type = DataType.HALF3 if component_type == DataType.HALF else DataType.FLOAT3
            three_comps = ['xyz', 'rgb']
            if size >= 4:
                three_comps.extend(['xyw', 'xzw', 'yzw', 'rga', 'rba', 'gba'])
            
            for comp in three_comps:
                if all(c in components + color_components for c in comp):
                    vector_node.children[comp] = TypeNode(comp, result_type, {}, {}, True)
        
        # 四个分量组合
        if size >= 4:
            result_type = DataType.HALF4 if component_type == DataType.HALF else DataType.FLOAT4
            four_comps = ['xyzw', 'rgba']
            
            for comp in four_comps:
                vector_node.children[comp] = TypeNode(comp, result_type, {}, {}, True)
    
    def _register_matrix_types(self):
        """注册矩阵类型"""
        matrix_types = [DataType.FLOAT3X3, DataType.FLOAT4X4]
        
        for matrix_type in matrix_types:
            node = TypeNode(
                name=matrix_type.value,
                data_type=matrix_type,
                children={},
                methods={},
                is_builtin=True
            )
            self.type_registry[matrix_type.value] = node
    
    def _register_texture_types(self):
        """注册纹理和采样器类型"""
        
        # texture2d 类型
        texture2d_node = TypeNode(
            name="texture2d",
            data_type=DataType.TEXTURE2D,
            children={},
            methods={},
            is_builtin=True
        )
        
        # 添加 sample 方法
        sample_method = MethodInfo(
            name="sample",
            return_type=DataType.FLOAT4,
            parameters=[DataType.SAMPLER, DataType.FLOAT2],
            description="纹理采样方法"
        )
        texture2d_node.methods["sample"] = sample_method
        
        self.type_registry["texture2d"] = texture2d_node
        
        # sampler 类型
        sampler_node = TypeNode(
            name="sampler",
            data_type=DataType.SAMPLER,
            children={},
            methods={},
            is_builtin=True
        )
        self.type_registry["sampler"] = sampler_node

    def _initialize_builtin_functions(self):
        """初始化着色器内置函数"""

        # 数学函数
        self._register_math_functions()

        # 类型转换函数
        self._register_type_constructor_functions()

        # 几何函数
        self._register_geometry_functions()

        # 纹理函数
        self._register_texture_functions()

    def _register_math_functions(self):
        """注册数学函数"""

        # 三角函数
        trig_functions = [
            ("sin", "正弦函数"),
            ("cos", "余弦函数"),
            ("tan", "正切函数"),
            ("asin", "反正弦函数"),
            ("acos", "反余弦函数"),
            ("atan", "反正切函数"),
            ("atan2", "双参数反正切函数"),
            ("sinh", "双曲正弦函数"),
            ("cosh", "双曲余弦函数"),
            ("tanh", "双曲正切函数"),
        ]

        for func_name, desc in trig_functions:
            # 单参数版本 - 返回与输入相同类型
            self.function_registry[func_name] = FunctionInfo(
                name=func_name,
                return_type=DataType.UNKNOWN,  # 动态推断，与输入类型相同
                parameters=[DataType.UNKNOWN],  # 接受任何数值类型
                is_builtin=True,
                description=desc
            )

            # atan2 需要两个参数
            if func_name == "atan2":
                self.function_registry[func_name].parameters = [DataType.UNKNOWN, DataType.UNKNOWN]

        # 指数和对数函数
        exp_log_functions = [
            ("exp", "自然指数函数"),
            ("exp2", "2的幂函数"),
            ("log", "自然对数函数"),
            ("log2", "以2为底的对数函数"),
            ("log10", "以10为底的对数函数"),
            ("sqrt", "平方根函数"),
            ("rsqrt", "平方根倒数函数"),
            ("pow", "幂函数"),
            ("powr", "幂函数(正数版本)"),
        ]

        for func_name, desc in exp_log_functions:
            if func_name in ["pow", "powr"]:
                # 两参数版本
                self.function_registry[func_name] = FunctionInfo(
                    name=func_name,
                    return_type=DataType.UNKNOWN,
                    parameters=[DataType.UNKNOWN, DataType.UNKNOWN],
                    is_builtin=True,
                    description=desc
                )
            else:
                # 单参数版本
                self.function_registry[func_name] = FunctionInfo(
                    name=func_name,
                    return_type=DataType.UNKNOWN,
                    parameters=[DataType.UNKNOWN],
                    is_builtin=True,
                    description=desc
                )

        # 取整和符号函数
        rounding_functions = [
            ("floor", "向下取整"),
            ("ceil", "向上取整"),
            ("round", "四舍五入"),
            ("trunc", "截断小数部分"),
            ("fract", "获取小数部分"),
            ("abs", "绝对值"),
            ("sign", "符号函数"),
        ]

        for func_name, desc in rounding_functions:
            self.function_registry[func_name] = FunctionInfo(
                name=func_name,
                return_type=DataType.UNKNOWN,
                parameters=[DataType.UNKNOWN],
                is_builtin=True,
                description=desc
            )

        # 比较和选择函数
        comparison_functions = [
            ("min", "最小值", 2),
            ("max", "最大值", 2),
            ("clamp", "限制在范围内", 3),
            ("mix", "线性插值", 3),
            ("step", "阶跃函数", 2),
            ("smoothstep", "平滑阶跃函数", 3),
        ]

        for func_name, desc, param_count in comparison_functions:
            self.function_registry[func_name] = FunctionInfo(
                name=func_name,
                return_type=DataType.UNKNOWN,
                parameters=[DataType.UNKNOWN] * param_count,
                is_builtin=True,
                description=desc
            )

    def _register_type_constructor_functions(self):
        """注册类型转换构造函数"""

        type_constructors = [
            ("float", DataType.FLOAT),
            ("half", DataType.HALF),
            ("int", DataType.INT),
            ("uint", DataType.UINT),
            ("bool", DataType.BOOL),
            ("float2", DataType.FLOAT2),
            ("float3", DataType.FLOAT3),
            ("float4", DataType.FLOAT4),
            ("half2", DataType.HALF2),
            ("half3", DataType.HALF3),
            ("half4", DataType.HALF4),
            ("float3x3", DataType.FLOAT3X3),
            ("float4x4", DataType.FLOAT4X4),
        ]

        for func_name, return_type in type_constructors:
            # 类型构造函数可以接受多种参数组合
            self.function_registry[func_name] = FunctionInfo(
                name=func_name,
                return_type=return_type,
                parameters=[],  # 参数可变，在推断时动态处理
                is_builtin=True,
                description=f"{return_type.value}类型构造函数"
            )

    def _register_geometry_functions(self):
        """注册几何函数"""

        # 向量函数
        vector_functions = [
            ("dot", DataType.FLOAT, 2, "点积"),
            ("cross", DataType.FLOAT3, 2, "叉积"),
            ("length", DataType.FLOAT, 1, "向量长度"),
            ("distance", DataType.FLOAT, 2, "两点距离"),
            ("normalize", DataType.UNKNOWN, 1, "向量归一化"),
            ("reflect", DataType.UNKNOWN, 2, "反射向量"),
            ("refract", DataType.UNKNOWN, 3, "折射向量"),
            ("faceforward", DataType.UNKNOWN, 3, "面向前方向量"),
        ]

        for func_name, return_type, param_count, desc in vector_functions:
            self.function_registry[func_name] = FunctionInfo(
                name=func_name,
                return_type=return_type,
                parameters=[DataType.UNKNOWN] * param_count,
                is_builtin=True,
                description=desc
            )

    def _register_texture_functions(self):
        """注册纹理函数"""

        # 纹理采样函数通常作为方法存在，这里注册一些全局纹理函数
        texture_functions = [
            ("texture", DataType.FLOAT4, 2, "纹理采样"),
            ("textureLod", DataType.FLOAT4, 3, "指定LOD的纹理采样"),
            ("textureGrad", DataType.FLOAT4, 4, "指定梯度的纹理采样"),
        ]

        for func_name, return_type, param_count, desc in texture_functions:
            self.function_registry[func_name] = FunctionInfo(
                name=func_name,
                return_type=return_type,
                parameters=[DataType.UNKNOWN] * param_count,
                is_builtin=True,
                description=desc
            )

    def parse_shader_content(self, shader_content: str):
        """解析着色器内容，识别结构体定义和变量声明"""
        lines = shader_content.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 解析结构体定义
            if line.startswith('struct '):
                i = self._parse_struct_definition(lines, i)
            # 解析函数定义
            elif self._is_function_definition(line):
                i = self._parse_function_definition(lines, i)
            # 解析函数参数中的变量声明
            elif 'main0(' in line or any(func in line for func in ['vertex ', 'fragment ']):
                self._parse_function_parameters(line)
                i += 1
            else:
                i += 1

    def _parse_struct_definition(self, lines: List[str], start_index: int) -> int:
        """解析结构体定义"""
        struct_line = lines[start_index].strip()

        # 提取结构体名称
        struct_match = re.match(r'struct\s+(\w+)', struct_line)
        if not struct_match:
            return start_index + 1

        struct_name = struct_match.group(1)

        # 创建结构体节点
        struct_node = TypeNode(
            name=struct_name,
            data_type=DataType.UNKNOWN,  # 自定义结构体类型
            children={},
            methods={},
            is_builtin=False
        )

        # 查找结构体开始的大括号
        i = start_index + 1
        while i < len(lines) and '{' not in lines[i]:
            i += 1

        if i >= len(lines):
            return start_index + 1

        # 解析结构体成员
        i += 1
        while i < len(lines):
            line = lines[i].strip()

            # 结构体结束
            if line.startswith('}'):
                break

            # 解析成员定义
            if line and not line.startswith('//'):
                member_node = self._parse_struct_member(line)
                if member_node:
                    struct_node.children[member_node.name] = member_node

            i += 1

        # 存储结构体定义
        self.type_registry[struct_name] = struct_node
        return i + 1

    def _parse_struct_member(self, line: str) -> Optional[TypeNode]:
        """解析结构体成员"""
        # 移除注释和属性
        line = re.sub(r'\[\[.*?\]\]', '', line)  # 移除 [[user(locn0)]] 等属性
        line = re.sub(r'//.*$', '', line)        # 移除注释
        line = line.strip().rstrip(';')

        # 匹配类型和名称
        match = re.match(r'(\w+(?:\d*)?)\s+(\w+)', line)
        if match:
            type_str = match.group(1)
            member_name = match.group(2)

            # 转换为DataType
            data_type = self._string_to_datatype(type_str)
            if data_type != DataType.UNKNOWN:
                return TypeNode(
                    name=member_name,
                    data_type=data_type,
                    children={},
                    methods={},
                    is_builtin=False
                )

        return None

    def _parse_function_parameters(self, line: str):
        """解析函数参数中的变量声明"""
        # 查找函数参数
        param_match = re.search(r'\((.*?)\)', line)
        if param_match:
            params = param_match.group(1)

            # 分割参数
            for param in params.split(','):
                param = param.strip()
                if param:
                    # 移除属性标记 [[...]]
                    param = re.sub(r'\[\[.*?\]\]', '', param).strip()

                    # 解析参数类型和名称
                    # 支持复杂的类型声明，如: constant _Block1T& _Block1, main0_in in
                    param_pattern = r'(?:constant\s+)?([a-zA-Z_][a-zA-Z0-9_]*)\s*[&*]?\s+([a-zA-Z_][a-zA-Z0-9_]*)'
                    match = re.match(param_pattern, param)

                    if match:
                        type_name = match.group(1)
                        var_name = match.group(2)

                        # 存储变量类型
                        self.variable_types[var_name] = type_name
                    else:
                        # 简单的解析方式作为后备
                        parts = param.split()
                        if len(parts) >= 2:
                            # 取最后一个作为变量名，倒数第二个作为类型
                            var_name = parts[-1]
                            type_name = parts[-2].rstrip('&*')  # 移除引用和指针符号

                            # 存储变量类型
                            self.variable_types[var_name] = type_name

    def _string_to_datatype(self, type_str: str) -> DataType:
        """将字符串转换为DataType"""
        type_map = {
            'float': DataType.FLOAT,
            'half': DataType.HALF,
            'int': DataType.INT,
            'uint': DataType.UINT,
            'bool': DataType.BOOL,
            'float2': DataType.FLOAT2,
            'float3': DataType.FLOAT3,
            'float4': DataType.FLOAT4,
            'half2': DataType.HALF2,
            'half3': DataType.HALF3,
            'half4': DataType.HALF4,
            'float3x3': DataType.FLOAT3X3,
            'float4x4': DataType.FLOAT4X4,
            'texture2d': DataType.TEXTURE2D,
            'sampler': DataType.SAMPLER,
        }
        return type_map.get(type_str, DataType.UNKNOWN)

    def _is_function_definition(self, line: str) -> bool:
        """判断是否是函数定义"""
        # 简单的函数定义模式匹配
        # 匹配: return_type function_name(parameters)
        pattern = r'^\s*(\w+(?:\d*)?)\s+(\w+)\s*\([^)]*\)\s*(?:\[\[.*?\]\])?\s*(?:{|$)'
        return bool(re.match(pattern, line))

    def _parse_function_definition(self, lines: List[str], start_index: int) -> int:
        """解析函数定义"""
        line = lines[start_index].strip()

        # 解析函数签名
        # 匹配: return_type function_name(parameters)
        pattern = r'^\s*(\w+(?:\d*)?)\s+(\w+)\s*\(([^)]*)\)\s*(?:\[\[.*?\]\])?\s*'
        match = re.match(pattern, line)

        if not match:
            return start_index + 1

        return_type_str = match.group(1)
        function_name = match.group(2)
        params_str = match.group(3)

        # 转换返回类型
        return_type = self._string_to_datatype(return_type_str)

        # 解析参数
        parameters = []
        if params_str.strip():
            for param in params_str.split(','):
                param = param.strip()
                if param:
                    # 解析参数类型
                    param_parts = param.split()
                    if len(param_parts) >= 1:
                        param_type_str = param_parts[0]
                        param_type = self._string_to_datatype(param_type_str)
                        parameters.append(param_type)

        # 创建函数信息
        func_info = FunctionInfo(
            name=function_name,
            return_type=return_type,
            parameters=parameters,
            is_builtin=False,
            description=f"用户定义函数: {function_name}"
        )

        # 存储函数定义
        self.function_registry[function_name] = func_info

        # 跳过函数体
        i = start_index + 1
        brace_count = 0
        found_opening_brace = False

        # 查找函数体开始
        while i < len(lines):
            current_line = lines[i].strip()
            if '{' in current_line:
                found_opening_brace = True
                brace_count += current_line.count('{')
                brace_count -= current_line.count('}')
                break
            i += 1

        if not found_opening_brace:
            return start_index + 1

        # 跳过函数体
        i += 1
        while i < len(lines) and brace_count > 0:
            current_line = lines[i].strip()
            brace_count += current_line.count('{')
            brace_count -= current_line.count('}')
            i += 1

        return i

    def infer_member_access_type(self, member_access: str) -> DataType:
        """推断成员访问的类型 - 核心功能"""
        parts = member_access.split('.')
        if len(parts) < 2:
            return DataType.UNKNOWN

        # 获取基础变量
        base_var = parts[0]

        # 查找基础变量的类型
        base_type_name = self.variable_types.get(base_var)
        if not base_type_name:
            return DataType.UNKNOWN

        # 获取基础类型节点
        current_node = self.type_registry.get(base_type_name)
        if not current_node:
            return DataType.UNKNOWN

        # 逐级查找成员
        for i in range(1, len(parts)):
            member_name = parts[i]

            # 在当前节点的子成员中查找
            if member_name in current_node.children:
                current_node = current_node.children[member_name]
            else:
                # 如果在当前节点找不到，尝试在当前节点的数据类型对应的内置类型中查找
                current_type_node = self.type_registry.get(current_node.data_type.value)
                if current_type_node and member_name in current_type_node.children:
                    current_node = current_type_node.children[member_name]
                else:
                    return DataType.UNKNOWN

        return current_node.data_type

    def infer_method_call_type(self, object_name: str, method_name: str, args: List[str] = None) -> DataType:
        """推断方法调用的返回类型"""
        if args is None:
            args = []

        # 查找对象类型
        object_type_name = self.variable_types.get(object_name)
        if not object_type_name:
            return DataType.UNKNOWN

        # 获取对象类型节点
        object_node = self.type_registry.get(object_type_name)
        if not object_node:
            return DataType.UNKNOWN

        # 查找方法
        if method_name in object_node.methods:
            method_info = object_node.methods[method_name]
            return method_info.return_type

        return DataType.UNKNOWN

    def infer_function_call_type(self, func_name: str, arg_types: List[DataType] = None) -> DataType:
        """推断函数调用的返回类型"""
        if arg_types is None:
            arg_types = []

        # 处理带命名空间前缀的函数名 (如 fast::normalize)
        base_func_name = func_name.split('::')[-1] if '::' in func_name else func_name

        # 查找函数定义
        if func_name in self.function_registry:
            func_info = self.function_registry[func_name]

            # 如果返回类型是UNKNOWN，说明需要动态推断
            if func_info.return_type == DataType.UNKNOWN:
                return self._infer_dynamic_function_return_type(base_func_name, arg_types)
            else:
                return func_info.return_type
        elif base_func_name in self.function_registry:
            func_info = self.function_registry[base_func_name]

            # 如果返回类型是UNKNOWN，说明需要动态推断
            if func_info.return_type == DataType.UNKNOWN:
                return self._infer_dynamic_function_return_type(base_func_name, arg_types)
            else:
                return func_info.return_type

        # 如果找不到函数定义，尝试动态推断
        dynamic_result = self._infer_dynamic_function_return_type(base_func_name, arg_types)
        if dynamic_result != DataType.UNKNOWN:
            return dynamic_result

        # 如果找不到函数定义，尝试作为类型构造函数处理
        constructor_type = self._string_to_datatype(func_name)
        if constructor_type != DataType.UNKNOWN:
            return constructor_type

        return DataType.UNKNOWN

    def _infer_dynamic_function_return_type(self, func_name: str, arg_types: List[DataType]) -> DataType:
        """推断动态返回类型的函数"""

        # 数学函数通常返回与第一个参数相同的类型
        math_functions = {
            'sin', 'cos', 'tan', 'asin', 'acos', 'atan', 'sinh', 'cosh', 'tanh',
            'exp', 'exp2', 'log', 'log2', 'log10', 'sqrt', 'rsqrt',
            'floor', 'ceil', 'round', 'trunc', 'fract', 'abs', 'sign',
            'normalize', 'reflect'
        }

        if func_name in math_functions and arg_types:
            return arg_types[0]

        # 双参数函数返回与参数兼容的类型
        binary_functions = {'min', 'max', 'pow', 'powr', 'atan2'}
        if func_name in binary_functions and len(arg_types) >= 2:
            # 返回更高精度的类型
            return self._get_higher_precision_type(arg_types[0], arg_types[1])

        # 三参数函数
        ternary_functions = {'clamp', 'mix', 'smoothstep', 'refract', 'faceforward'}
        if func_name in ternary_functions and arg_types:
            return arg_types[0]  # 通常返回第一个参数的类型

        # step函数返回与第二个参数相同的类型
        if func_name == 'step' and len(arg_types) >= 2:
            return arg_types[1]

        # 默认返回float
        return DataType.FLOAT

    def _get_higher_precision_type(self, type1: DataType, type2: DataType) -> DataType:
        """获取两个类型中精度更高的类型"""
        # 精度优先级: float > half, 向量维度高的优先
        precision_order = {
            DataType.HALF: 1, DataType.FLOAT: 2,
            DataType.HALF2: 3, DataType.FLOAT2: 4,
            DataType.HALF3: 5, DataType.FLOAT3: 6,
            DataType.HALF4: 7, DataType.FLOAT4: 8,
        }

        order1 = precision_order.get(type1, 0)
        order2 = precision_order.get(type2, 0)

        if order1 >= order2:
            return type1
        else:
            return type2

    def register_variable(self, var_name: str, type_name: str):
        """注册变量类型"""
        self.variable_types[var_name] = type_name

    def get_variable_type(self, var_name: str) -> Optional[str]:
        """获取变量类型名"""
        return self.variable_types.get(var_name)

    def get_type_info(self, type_name: str) -> Optional[TypeNode]:
        """获取类型信息"""
        return self.type_registry.get(type_name)

    def list_type_members(self, type_name: str) -> List[str]:
        """列出类型的所有成员"""
        type_node = self.type_registry.get(type_name)
        if type_node:
            return list(type_node.children.keys())
        return []

    def list_type_methods(self, type_name: str) -> List[str]:
        """列出类型的所有方法"""
        type_node = self.type_registry.get(type_name)
        if type_node:
            return list(type_node.methods.keys())
        return []

    def get_function_info(self, func_name: str) -> Optional[FunctionInfo]:
        """获取函数信息"""
        return self.function_registry.get(func_name)

    def list_all_functions(self) -> List[str]:
        """列出所有已注册的函数"""
        return list(self.function_registry.keys())

    def list_builtin_functions(self) -> List[str]:
        """列出所有内置函数"""
        return [name for name, info in self.function_registry.items() if info.is_builtin]

    def list_user_functions(self) -> List[str]:
        """列出所有用户定义函数"""
        return [name for name, info in self.function_registry.items() if not info.is_builtin]

    def debug_print_registry(self):
        """调试：打印类型注册表"""
        print("=== 类型注册表 ===")
        for type_name, type_node in self.type_registry.items():
            print(f"\n类型: {type_name} ({type_node.data_type.value})")
            if type_node.children:
                print(f"  成员: {list(type_node.children.keys())}")
            if type_node.methods:
                print(f"  方法: {list(type_node.methods.keys())}")

        print(f"\n=== 变量类型映射 ===")
        for var_name, type_name in self.variable_types.items():
            print(f"  {var_name} -> {type_name}")

        print(f"\n=== 函数注册表 ===")
        builtin_funcs = self.list_builtin_functions()
        user_funcs = self.list_user_functions()

        if builtin_funcs:
            print(f"内置函数 ({len(builtin_funcs)}):")
            for func_name in sorted(builtin_funcs):
                func_info = self.function_registry[func_name]
                param_count = len(func_info.parameters)
                return_type = func_info.return_type.value
                print(f"  {func_name}({param_count} 参数) -> {return_type}")

        if user_funcs:
            print(f"用户定义函数 ({len(user_funcs)}):")
            for func_name in sorted(user_funcs):
                func_info = self.function_registry[func_name]
                param_types = [p.value for p in func_info.parameters]
                return_type = func_info.return_type.value
                print(f"  {func_name}({', '.join(param_types)}) -> {return_type}")


# 全局类型推断引擎实例
type_inference_engine = TypeInferenceEngine()
